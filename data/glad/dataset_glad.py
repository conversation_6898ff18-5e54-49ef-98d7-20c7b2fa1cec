"""
Unified Dataset Loading for Water Body Spatiotemporal Modeling with GLAD Probability Data

This module implements:
1. Unified data loading for training and inference with GLAD probability data
2. Integration with balanced sampling index
3. Missing pattern database integration for data augmentation
4. 120-frame temporal window support (60 before + 60 after)
5. Multi-modal feature fusion (water probability masks, occurrence, geo/temporal features)
6. Efficient data loading with proper padding and normalization
7. Direct probability learning (0-100 values) without binary conversion
"""

import os
import numpy as np
import pandas as pd
import xarray as xr
import torch
from torch.utils.data import Dataset, DataLoader, Sampler
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any
import logging
import json
import random
from concurrent.futures import ThreadPoolExecutor
from collections import defaultdict
import warnings
from model.loss import compute_dynamic_degree

warnings.filterwarnings('ignore')
logger = logging.getLogger(__name__)

# GLAD data value definitions (0-100 probability values)
GLAD_VALUES = {
    'no_data': 255,
    'land': 0,      # 0 = 不可能是水
    'water': 100,   # 100 = 很可能是水
    'no_observation': 255  # 无观测数据也标记为255
}

# Model input value definitions for probability learning
MODEL_VALUES = {
    'missing': -1,  # Missing data
    'land': 0,      # Land (0 probability)
    'water': 100    # Water (100 probability)
}


class DataProcessor:
    """Handles data preprocessing and normalization for probability learning"""
    
    def __init__(self):
        """Initialize data processor"""
        pass
    
    def preprocess_glad_data(self, data: np.ndarray) -> np.ndarray:
        """
        Preprocess GLAD water probability data (0-100) for direct probability learning
        
        Args:
            data: Raw GLAD data (0-100 probability values)
            
        Returns:
            processed_data: Processed data with values [0, 100] or -1 for missing
        """
        # Create output array
        processed = np.full_like(data, MODEL_VALUES['land'], dtype=np.float32)
                
        # Keep probability values as-is for direct learning
        # 0 = 不可能是水 -> 0 (非水)
        # 1-100 = 水体概率 -> 1-100 (water probability)
        # 255 = 无数据 -> -0 (非水)
        water_mask = (data > GLAD_VALUES['land']) & (data <= GLAD_VALUES['water'])
        processed[water_mask] = data[water_mask]
        
        return processed
    
    def normalize_glad_occurrence_data(self, occ_data: np.ndarray) -> np.ndarray:
        """Normalize GLAD occurrence data to [0, 1] range"""
        # Handle no-data values
        valid_mask = occ_data != GLAD_VALUES['no_data']
        normalized = np.zeros_like(occ_data, dtype=np.float32)
        normalized[valid_mask] = occ_data[valid_mask] / 100.0
        normalized[~valid_mask] = MODEL_VALUES['missing']  # Fill invalid with missing
        
        return normalized
    
    def create_temporal_features(self, timestamps: List[pd.Timestamp]) -> np.ndarray:
        """
        Create temporal features from timestamps
        
        Args:
            timestamps: List of timestamps
            
        Returns:
            Temporal features array (T, num_features)
        """
        features = []
        
        for ts in timestamps:
            # Cyclical encoding for month
            month_sin = np.sin(2 * np.pi * ts.month / 12)
            month_cos = np.cos(2 * np.pi * ts.month / 12)
            
            # Year as normalized value
            year_norm = (ts.year - 1984) / (2021 - 1984)  # JRC range
            
            feature_vec = [month_sin, month_cos, year_norm]
            features.append(feature_vec)
        
        return np.array(features, dtype=np.float32)
    
    def create_geo_features(self, lon: float, lat: float) -> np.ndarray:
        """
        Create compact geographic feature vector (no spatial dimensions)
        
        Args:
            lon: Longitude
            lat: Latitude
        Returns:
            1-D geographic feature vector (4,)
        """
        lon_norm = (lon + 180) / 360  # Normalize to [0,1]
        lat_norm = (lat + 90) / 180
        # Placeholder local coords (center of tile)
        x_center = 0.5
        y_center = 0.5
        return np.array([lon_norm, lat_norm, x_center, y_center], dtype=np.float32)

class MissingPatternDatabase:
    """Missing pattern database"""
    
    def __init__(self, missing_db_file: Path):
        self.missing_db_file = Path(missing_db_file)
        self.missing_db = self._load_missing_db()
    
    def _load_missing_db(self):
        """Load missing pattern database"""
        if not self.missing_db_file or not self.missing_db_file.exists():
            logger.warning("Missing pattern database not found")
            return None
        
        data = np.load(self.missing_db_file, allow_pickle=True)
        self.patterns = data['patterns']
        self.config = data['config'].item()

        logger.info(f"Loaded missing pattern database with {self.config['total_patterns']} patterns")
        return self.patterns
    
    def get_random_pattern(self):
        """Get a random pattern with a specific missing ratio"""        
        random_pattern = random.choice(self.patterns)
        return random_pattern['pattern'], random_pattern['missing_ratio']

class WaterBodyDataset(Dataset):
    """Unified dataset for water body spatiotemporal modeling with GLAD probability data"""
    
    def __init__(self, 
                 index_file: Union[str, Path],
                 missing_db_file: Optional[Union[str, Path]] = None,
                 config: Optional[Dict] = None,
                 mode: str = 'train',
                 use_missing_augmentation: bool = True):
        """
        Initialize dataset for GLAD probability data
        
        Args:
            index_file: Path to balanced dataset index
            missing_db_file: Path to missing pattern database
            config: Configuration dictionary
            mode: Dataset mode ('train', 'val', 'test')
            use_missing_augmentation: Whether to use missing data augmentation
        """
        self.index_file = Path(index_file)
        self.missing_db_file = Path(missing_db_file) if missing_db_file else None
        self.mode = mode
        self.use_missing_augmentation = use_missing_augmentation
        
        # Default configuration
        default_config = {
            'data': {
                'sequence_length': 120,
                'tile_size': 256,
                'augment': False
            },
            'model': {
                'tasks': ['inpaint', 'predict', 'change']
            }
        }
        
        self.config = config or default_config
        self.sequence_length = self.config.get('data', {}).get('sequence_length', 120)
        self.tile_size = self.config.get('data', {}).get('tile_size', 256)
        self.augment = False
        
        # Initialize components
        self.data_processor = DataProcessor()
        
        # Load index
        self._load_index()
        
        # Load missing pattern database
        self.missing_db = self._load_missing_db()
        
        logger.info(f"Initialized WaterBodyDataset in {mode} mode with {len(self.samples)} samples")
        logger.info(f"Data format: GLAD probability (0-100)")
        logger.info(f"Missing augmentation enabled: {self.use_missing_augmentation}")
    
    def _load_index(self):
        """Load balanced dataset index"""
        if not self.index_file.exists():
            raise FileNotFoundError(f"Index file not found: {self.index_file}")
        
        with open(self.index_file, 'r') as f:
            index_data = json.load(f)
        
        # Use no-missing samples as our primary data source
        # These are water body images with no missing data (ground truth)
        self.no_missing_samples = index_data.get('no_missing_samples', {})

        if not self.no_missing_samples:
            raise ValueError(f"No no-missing samples found in index file: {self.index_file}")
            
        # Flatten samples from all files
        self.samples = []
        for file_path, file_samples in self.no_missing_samples.items():
            for sample in file_samples:
                # Mark as no-missing for reference
                sample['is_no_missing'] = True
                # Ensure file_path key exists (compatibility with IndexBuilder output)
                if 'file_path' not in sample:
                    # In newer IndexBuilder versions, samples may use 'source_file'
                    sample['file_path'] = file_path

                # ------------------------------------------------------------------
                # Defensive type conversion ------------------------------------------------
                # Some index builders may accidentally export numeric indices as strings.
                # When these values are used for NumPy / xarray fancy indexing later on,
                # a string dtype (e.g. '<U1') will trigger the runtime error we observed:
                #   "invalid indexer array, does not have integer dtype".
                # To harden the pipeline we convert them to Python ints here.
                # ------------------------------------------------------------------
                for key in ('idx_x', 'idx_y', 'time_idx'):
                    if key in sample and not isinstance(sample[key], (int, np.integer)):
                        try:
                            sample[key] = int(sample[key])
                        except Exception as type_err:
                            logger.warning(
                                f"Failed to cast {key}='{sample[key]}' to int for sample from {file_path}: {type_err}")
                self.samples.append(sample)
            
        logger.info(f"Loaded {len(self.samples)} no-missing samples")
        
        # Split by mode
        total_samples = len(self.samples)

        if self.mode == 'train':
            self.samples = self.samples[:int(0.8 * total_samples)]
        elif self.mode == 'val':
            self.samples = self.samples[int(0.8 * total_samples):int(0.9 * total_samples)]
        elif self.mode == 'debug':
            self.samples = self.samples[:500]
        elif self.mode == 'test':  # test
            self.samples = self.samples[int(0.9 * total_samples):]
        
        logger.info(f"Using {len(self.samples)} {self.mode} samples")

    # NEW: Helper to fetch mean_water_frequency for a sample
    def _fetch_mean_water_frequency(self, sample: Dict[str, Any]) -> float:
        """Read mean_water_frequency for given sample location"""
        file_path = sample['file_path']
        idx_x = int(sample['idx_x'])
        idx_y = int(sample['idx_y'])
        try:
            with xr.open_dataset(file_path) as ds:
                mean_freq = float(ds.mean_water_frequency[idx_x, idx_y].values)
        except Exception as e:
            logger.warning(f"Error reading mean_water_frequency from {file_path}: {e}")
            mean_freq = 0.5  # Neutral value
        return mean_freq
    
    def _load_missing_db(self):
        """Load missing pattern database"""
        if not self.missing_db_file or not self.missing_db_file.exists() or not self.use_missing_augmentation:
            logger.warning("Missing pattern database not used or not found")
            return None
            
        try:
            missing_db = MissingPatternDatabase(self.missing_db_file)
            return missing_db
        except Exception as e:
            logger.error(f"Failed to load missing pattern database: {e}")
            return None
    
    def __len__(self) -> int:
        """Return dataset size"""
        return len(self.samples)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """
        Get a single sample
        
        Args:
            idx: Sample index
            
        Returns:
            Dictionary containing model inputs
        """
        sample_info = self.samples[idx]
        
        try:
            # Load data from NetCDF file
            sample_data = self._load_sample_data(sample_info)
            
            # Process and augment data
            processed_sample = self._process_sample(sample_data)
            
            return processed_sample
            
        except Exception as e:
            logger.error(f"Error loading sample {idx}: {e}")
            # Return a dummy sample to avoid breaking the batch
            return self._create_dummy_sample()
    
    def _load_sample_data(self, sample_info: Dict[str, Any]) -> Dict[str, Any]:
        """Load data for a single sample from NetCDF file"""
        file_path = sample_info['file_path']
        
        # # Fix path prefix: replace old path with new path
        # if file_path.startswith('/fossfs/xiaozhen/Clip/JRC4/'):
        #     file_path = file_path.replace('/fossfs/xiaozhen/Clip/JRC4/', '/scr/u/xiaoz/Clip/')
            
        # Ensure indices are proper integers – cast defensively to avoid string dtypes
        idx_x = int(sample_info['idx_x'])
        idx_y = int(sample_info['idx_y'])
        center_time_idx = int(sample_info['time_idx'])
        
        with xr.open_dataset(file_path, chunks={'time': 10}) as ds:
            # Get time dimension size
            time_size = ds.dims['time']
            
            # Calculate temporal window bounds centered on the sample
            half_seq = self.sequence_length // 2
            start_time = max(0, center_time_idx - half_seq)
            end_time = min(time_size, center_time_idx + half_seq)
            
            # Ensure we get exactly sequence_length frames
            if end_time - start_time < self.sequence_length:
                if start_time == 0:
                    end_time = min(time_size, self.sequence_length)
                else:
                    start_time = max(0, end_time - self.sequence_length)
            
            # Load temporal sequence data
            data = ds.data[idx_x, idx_y, start_time:end_time].values
            occ_data = ds.occ_data[idx_x, idx_y].values
            
            # Get time coordinates
            time_coords = pd.to_datetime(ds.time[start_time:end_time].values)
            
            # Get static features
            mean_freq = float(ds.mean_water_frequency[idx_x, idx_y].values)
            tile_lon = float(ds.tile_lon[idx_x, idx_y].values)
            tile_lat = float(ds.tile_lat[idx_x, idx_y].values)
            
            # Calculate the center frame index in the loaded sequence
            center_frame_idx = int(center_time_idx - start_time)
        
        return {
            'data': data,
            'occ_data': occ_data,
            'time_coords': time_coords,
            'mean_water_frequency': mean_freq,
            'tile_lon': tile_lon,
            'tile_lat': tile_lat,
            'center_frame_idx': center_frame_idx
        }
    
    def _process_sample(self, sample_data: Dict[str, Any]):
        """Process raw sample data into model inputs for probability learning"""
        # Extract data
        raw_data = sample_data['data']  # (T, H, W) - GLAD probability values
        occ_data = sample_data['occ_data']  # (H, W)
        time_coords = sample_data['time_coords']
        center_frame_idx = sample_data['center_frame_idx']
        
        # Preprocess GLAD data for probability learning
        processed_data = self.data_processor.preprocess_glad_data(raw_data)
        
        # ------------------------------------------------------------------
        # Build two-channel representation for probability learning
        #   ch0: water probability (0-100)  (missing treated as 0)
        #   ch1: missing mask              (missing=1, otherwise 0)
        # ------------------------------------------------------------------
        # Normalize probability values to [0, 1] range for model input
        if processed_data.max() > 1:
            probability_channel = np.clip(processed_data / 100.0, 0, 1).astype(np.float32)
        else:
            probability_channel = processed_data.astype(np.float32)
        missing_channel_full = (raw_data == GLAD_VALUES['no_data'])
        missing_channel = missing_channel_full.astype(np.float32)

        two_channel_data = np.stack([probability_channel, missing_channel], axis=1)  # (T, 2, H, W)

        # Generate ground-truth probability for center frame (single channel)
        if 0 <= center_frame_idx < processed_data.shape[0]:
            ground_truth_raw = probability_channel[center_frame_idx].copy()
        else:
            mid_idx = probability_channel.shape[0] // 2
            ground_truth_raw = probability_channel[mid_idx].copy()

        # Keep ground truth as probability values (0-1)
        ground_truth = ground_truth_raw.astype(np.float32)  # (H,W) - probability values
        
        # The input sequence is a copy that will be augmented
        augmented_data = two_channel_data.copy()  # work on 2-channel
        
        # Prepare full-sequence missing mask (True = missing) for loss/metrics
        missing_mask_center = missing_channel_full[center_frame_idx]  # boolean (H,W)
        
        # This will also hold artificial missing pixels injected later (only center frame)
        artificial_mask_center = np.zeros_like(ground_truth_raw, dtype=bool)

        # Apply pattern to the center frame
        if 0 <= center_frame_idx < processed_data.shape[0]:
            # Get a random missing pattern
            missing_pattern, _ = self.missing_db.get_random_pattern()
            
            if missing_pattern is not None:
                # Insert artificial missing pixels into center frame mask
                artificial_mask_center = missing_pattern.astype(bool)
                
                # Apply mask to probability channel (set to 0) and mark missing channel =1
                augmented_data[center_frame_idx, 0][artificial_mask_center] = 0.0
                augmented_data[center_frame_idx, 1][artificial_mask_center] = 1.0
                
                # Update center-frame missing mask
                missing_mask_center |= artificial_mask_center
        
        # Process occurrence data
        normalized_occ = self.data_processor.normalize_glad_occurrence_data(occ_data)
                
        # Extract geographic and temporal information for new model
        tile_lon = sample_data['tile_lon']
        tile_lat = sample_data['tile_lat']
        
        # Extract year and month from center frame timestamp
        center_timestamp = time_coords[center_frame_idx] if 0 <= center_frame_idx < len(time_coords) else time_coords[len(time_coords)//2]
        year = center_timestamp.year
        month = center_timestamp.month
        
        # Ensure proper sequence length with padding if needed
        T = augmented_data.shape[0]
        if T < self.sequence_length:
            # Pad sequence (probability channel 0, missing channel 1) with zeros / ones accordingly
            pad_size = self.sequence_length - T
            pad_shape = (pad_size,) + augmented_data.shape[1:]
            pad_block = np.zeros(pad_shape, dtype=augmented_data.dtype)
            augmented_data = np.concatenate([
                augmented_data,
                pad_block
            ], axis=0)
            # NOTE: missing_mask_center 与 temporal_features 为中心帧 / 静态信息，无需填充
        
        # Convert to tensors - ground_truth现在保持概率值
        return {
            # Core sequence data
            'input_sequence': torch.from_numpy(augmented_data).float(),
            'missing_mask': torch.from_numpy(missing_mask_center).bool(),
            'ground_truth': torch.from_numpy(ground_truth).float(),  # (H,W) probability values (0-100)
            'occurrence': torch.from_numpy(normalized_occ).float(),
            'center_frame_idx': torch.tensor(center_frame_idx).long(),
            
            # Geographic and temporal context (new format for enhanced model)
            'tile_lon': torch.tensor(tile_lon).float(),
            'tile_lat': torch.tensor(tile_lat).float(),
            'year': torch.tensor(year).long(),
            'month': torch.tensor(month).long(),
        }
    
    def _create_dummy_sample(self) -> Dict[str, torch.Tensor]:
        """Create a dummy sample for error recovery"""
        T, H, W = self.sequence_length, self.tile_size, self.tile_size
        
        return {
            # Core sequence data (dummy with two channels)
            'input_sequence': torch.zeros(T, 2, H, W).float(),
            'missing_mask': torch.zeros(H, W).bool(),
            'ground_truth': torch.zeros(H, W).float(),  # Probability values (0-100)
            'occurrence': torch.zeros(H, W).float(),
            'center_frame_idx': torch.tensor(-1).long(),
            
            # Geographic and temporal context (dummy values)
            'tile_lon': torch.tensor(0.0).float(),
            'tile_lat': torch.tensor(0.0).float(),
            'year': torch.tensor(2020).long(),
            'month': torch.tensor(6).long(),
            
            # Legacy features (for backward compatibility)
            'temporal_features': torch.zeros(1, 3).float(),
            'geo_features': torch.zeros(4).float(),
        }

# --------------------------- Curriculum Sampler ---------------------------
class CurriculumSampler(Sampler):
    """Sampler that gradually exposes samples with higher dynamic_degree as epochs progress."""
    def __init__(self,
                 dataset: 'WaterBodyDataset',
                 start_fraction: float = 0.3,
                 end_fraction: float = 1.0,
                 total_epochs: int = 20,
                 shuffle_within: bool = True):
        if not hasattr(dataset, 'samples'):
            raise ValueError("Dataset must have 'samples' attribute with dynamic_degree.")
        self.dataset = dataset
        self.start_fraction = max(0.0, min(start_fraction, 1.0))
        self.end_fraction = max(self.start_fraction, min(end_fraction, 1.0))
        self.total_epochs = max(1, total_epochs)
        self.shuffle_within = shuffle_within
        # Pre-compute indices sorted by dynamic_degree (dataset is already sorted)
        self.indices_sorted = list(range(len(dataset)))
        self.epoch = 0

    def set_epoch(self, epoch: int):
        """Update current epoch (call at start of every epoch)."""
        self.epoch = epoch

    def _current_cutoff(self) -> int:
        # Linear schedule
        frac = self.start_fraction + (self.end_fraction - self.start_fraction) * min(self.epoch, self.total_epochs - 1) / (self.total_epochs - 1)
        cutoff = int(len(self.dataset) * frac)
        cutoff = max(1, min(cutoff, len(self.dataset)))
        return cutoff

    def __iter__(self):
        cutoff = self._current_cutoff()
        subset = self.indices_sorted[:cutoff]
        if self.shuffle_within:
            g = torch.Generator()
            g.manual_seed(self.epoch)
            subset = torch.randperm(cutoff, generator=g).tolist()
        return iter(subset)

    def __len__(self):
        return self._current_cutoff()


# ------------------ NEW: CurriculumDistributedSampler ------------------
class CurriculumDistributedSampler(Sampler):
    """Distributed version of CurriculumSampler that works with DDP.

    Each epoch increases the visible fraction of the dataset (easy→hard) in a
    synchronized way across all ranks, then performs the usual distributed
    slicing so that every rank sees a unique subset.
    """

    def __init__(self,
                 dataset: 'WaterBodyDataset',
                 num_replicas: int,
                 rank: int,
                 start_fraction: float = 0.3,
                 end_fraction: float = 1.0,
                 total_epochs: int = 20,
                 shuffle: bool = True,
                 seed: int = 0):
        if not hasattr(dataset, 'samples'):
            raise ValueError("Dataset must have 'samples' attribute with dynamic_degree.")

        self.dataset = dataset
        self.num_replicas = num_replicas
        self.rank = rank
        self.start_fraction = max(0.0, min(start_fraction, 1.0))
        self.end_fraction = max(self.start_fraction, min(end_fraction, 1.0))
        self.total_epochs = max(1, total_epochs)
        self.shuffle = shuffle
        self.seed = seed

        # ---------------- 内置排序逻辑 ----------------
        # 计算每个样本的动态程度并排序
        dynamic_samples = []
        for sample in dataset.samples:
            try:
                # 使用已有的 mean_water_frequency（如果存在）
                if 'mean_water_frequency' in sample and sample['mean_water_frequency'] is not None:
                    mean_freq = float(sample['mean_water_frequency'])
                else:
                    # 从数据文件中读取 mean_water_frequency
                    mean_freq = self._fetch_mean_water_frequency(sample)
                
                # 计算动态程度
                sample['dynamic_degree'] = float(compute_dynamic_degree(torch.tensor(mean_freq)).item())
            except Exception as e:
                logger.warning(f"Failed to compute dynamic degree for sample {sample.get('file_path', 'N/A')}: {e}")
                sample['dynamic_degree'] = 1.0  # 降级到最高难度
            dynamic_samples.append(sample)
        
        # 按动态程度升序排序（简单→困难）
        dynamic_samples.sort(key=lambda s: s['dynamic_degree'])
        
        # 创建排序后的索引列表
        self.indices_sorted = list(range(len(dynamic_samples)))
        self.epoch = 0

    def _fetch_mean_water_frequency(self, sample: Dict[str, Any]) -> float:
        """从数据文件中读取 mean_water_frequency"""
        file_path = sample['file_path']
        idx_x = int(sample['idx_x'])
        idx_y = int(sample['idx_y'])
        try:
            with xr.open_dataset(file_path) as ds:
                mean_freq = float(ds.mean_water_frequency[idx_x, idx_y].values)
        except Exception as e:
            logger.warning(f"Error reading mean_water_frequency from {file_path}: {e}")
            mean_freq = 0.5  # 中性值
        return mean_freq

    def set_epoch(self, epoch: int):
        self.epoch = epoch

    # Helper to compute current visible subset size
    def _current_cutoff(self):
        frac = self.start_fraction + (self.end_fraction - self.start_fraction) * \
               min(self.epoch, self.total_epochs - 1) / (self.total_epochs - 1)
        cutoff = int(len(self.dataset) * frac)
        return max(1, min(cutoff, len(self.dataset)))

    def __iter__(self):
        # Determine subset for this epoch
        cutoff = self._current_cutoff()
        indices = self.indices_sorted[:cutoff]

        # Optionally shuffle using shared seed so all ranks shuffle the same list
        if self.shuffle:
            g = torch.Generator()
            g.manual_seed(self.seed + self.epoch)
            indices = torch.randperm(cutoff, generator=g).tolist()

        # Pad so that it is evenly divisible across replicas
        total_size = int(np.ceil(len(indices) / self.num_replicas)) * self.num_replicas
        if len(indices) < total_size:
            indices += indices[: (total_size - len(indices))]

        # Subsample for this rank
        rank_indices = indices[self.rank:total_size:self.num_replicas]
        return iter(rank_indices)

    def __len__(self):
        # Return length of this rank's subset
        return int(np.ceil(self._current_cutoff() / self.num_replicas))
    