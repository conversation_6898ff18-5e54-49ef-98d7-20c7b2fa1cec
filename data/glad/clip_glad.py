"""
优化的GLAD数据处理工具 - 大窗口版本

基于JRC处理代码修改，适应GLAD数据格式：
- GLAD数据路径格式：/path/to/GLAD/80N_170W/2019_11_percent.tif
- 目录名包含经纬度信息（如80N_170W）
- 文件名包含年月信息（如2019_11_percent.tif）

主要特性：
1. 大窗口概念：
   - 固定32x32 tiles的大窗口，减少内存占用
   - 大窗口间支持overlap处理
   - 邻近图像数据读取支持

2. 窗口计算优化：
   - 参考原始calculate_tile_window函数的简洁实现
   - rasterio自动处理边界填充
   - 简化的窗口计算逻辑

3. 邻近图像处理：
   - 通过lon_lat坐标判断邻近图像
   - 自动读取邻近图像数据补充边缘
   - 保持数据完整性

4. 性能优化：
   - 一次性读取大窗口数据到内存
   - 优化的并行处理流程
   - 高效的内存使用模式

输出NetCDF文件变量说明：
======================

坐标维度：
- idx_x, idx_y: tile在大窗口内的索引坐标（从0开始的整数）
- time: 时间维度，格式为 YYYY-MM-01
- y, x: tile内部像素坐标（0–255）

数据变量：
- data (idx_x, idx_y, time, y, x)
  * 含义：GLAD原始水体概率数据
  * 数据类型：uint8
  * 取值：0–100 表示水体概率百分比，255 表示无数据
  * 备注：0=无水体；1–100=水体概率；255=NoData

- water_proportion (idx_x, idx_y, time)
  * 含义：每个tile在每个时间步的水体像素比例
  * 数据类型：float32，范围 0.0–1.0
  * 计算：(像素值 > WATER_THRESHOLD 且 <= 100 的像素数) / (有效像素数)

- missing_proportion (idx_x, idx_y, time)
  * 含义：每个tile在每个时间步的无数据像素比例
  * 数据类型：float32，范围 0.0–1.0
  * 计算：(像素值 == 255 的像素数) / (总像素数)

- mean_water_frequency (idx_x, idx_y)
  * 含义：每个tile的平均水体频率
  * 数据类型：float32，范围 0.0–1.0
  * 计算：对 occ_data 的有效像素求平均后除以 100.0

- occ_data (idx_x, idx_y, y, x)
  * 含义：像素级水体出现频率（按时间序列计算）
  * 数据类型：uint8
  * 取值：0–100 表示水体出现频率百分比，255 表示无数据
  * 计算：每像素 (值 > WATER_THRESHOLD 的观测次数) / (有效观测次数) * 100
  * 特别说明：当整个tile的occ_data全为 0 或全为 100 时也会被写入；仅当全为 255(NoData) 时跳过

空间定位变量：
- tile_col_offset, tile_row_offset (idx_x, idx_y)
  * 含义：每个tile在大窗口内部的像素偏移量（列/行）
  * 用途：用于将tile定位回原始影像窗口

- tile_lon, tile_lat (idx_x, idx_y)
  * 含义：每个tile中心位置的经纬度（度）
  * 计算：使用 window_transform 对 (tile_col_offset + TILE_SIZE//2, tile_row_offset + TILE_SIZE//2) 做仿射变换
  * 注：上述为中心像素的左上角坐标；如需像素几何中心，可在列/行索引上各加 0.5 后再变换

全局属性（attrs）：
- window_transform: 大窗口的仿射变换参数（6个参数）
- window_width, window_height: 大窗口的实际像素尺寸
- window_lon, window_lat: 大窗口左上角经纬度
- tile_size: tile尺寸（256）
- tile_overlap: tile重叠像素（16）
- large_window_size: 大窗口尺寸


"""

import os
import numpy as np
import pandas as pd
import rasterio
from rasterio.windows import Window
from rasterio.transform import Affine
import glob
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
import re
from tqdm import tqdm
from contextlib import contextmanager
import xarray as xr
import logging
import traceback
import json
from rasterio.crs import CRS
import multiprocessing
from multiprocessing import Pool
import gc  # 显式垃圾回收控制
import threading
from collections import defaultdict, OrderedDict
from joblib import Parallel, delayed
import psutil  # 用于获取系统内存总量
import time
import atexit
import argparse

# -------------------------------------------------
# 线程局部LRU缓存 DatasetReader，避免重复打开同一文件
# -------------------------------------------------

_thread_local = threading.local()

# 最大缓存的文件数量
MAX_DS_CACHE_SIZE = 500

class LRUCache:
    """简单的LRU (Least Recently Used) 缓存，限制打开的文件数量"""

    def __init__(self, capacity):
        self.cache = OrderedDict()
        self.capacity = capacity

    def get(self, key):
        if key not in self.cache:
            return None

        # 更新使用顺序（移到最后 = 最近使用）
        self.cache.move_to_end(key)
        return self.cache[key]

    def put(self, key, value):
        # 如果已存在，更新值并移到队尾
        if key in self.cache:
            self.cache[key] = value
            self.cache.move_to_end(key)
            return

        # 如果缓存已满，删除最久未使用的条目
        if len(self.cache) >= self.capacity:
            # 取出队首元素（最久未使用的）
            oldest_key, oldest_value = self.cache.popitem(last=False)
            # 安全关闭数据集
            try:
                if hasattr(oldest_value, 'close') and not oldest_value.closed:
                    oldest_value.close()
                    logger.debug(f"Closed dataset: {oldest_key}")
            except Exception as e:
                logger.warning(f"Error closing dataset {oldest_key}: {e}")

        # 添加新项到队尾
        self.cache[key] = value

    def close_all(self):
        """关闭所有缓存的dataset"""
        for key, ds in list(self.cache.items()):
            try:
                if hasattr(ds, 'close') and not ds.closed:
                    ds.close()
            except Exception as e:
                logger.warning(f"Error closing dataset {key}: {e}")
        self.cache.clear()

def get_dataset(path: str):
    """返回缓存的 rasterio DatasetReader；若不存在则打开并缓存。
    使用LRU策略限制打开的文件数量。"""

    # 初始化线程局部LRU缓存
    if not hasattr(_thread_local, "ds_cache"):
        _thread_local.ds_cache = LRUCache(MAX_DS_CACHE_SIZE)

    # 从缓存获取
    ds = _thread_local.ds_cache.get(path)

    # 如果不存在或已关闭，则打开并缓存
    if ds is None or ds.closed:
        try:
            ds = rasterio.open(path, "r", sharing=True)
            _thread_local.ds_cache.put(path, ds)
        except Exception as e:
            logger.error(f"Error opening dataset {path}: {e}")
            raise

    return ds

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/process_dataset.log', mode='w', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# 常量配置
VALID_YEARS = {str(x) for x in range(2000, 2025)}  # GLAD数据年份范围
VALID_MONTHS = {str(x).zfill(2) for x in range(1, 13)}
GLAD_NODATA = 255  # GLAD无数据值
WATER_THRESHOLD = 50 # 水体阈值
GLAD_WATER_MIN = 0   # GLAD水体值（假设percent表示水体百分比）
GLAD_WATER_MAX = 100   # GLAD水体值（假设percent表示水体百分比）
TILE_SIZE = 256
OVERLAP = 16

# 大窗口参数
MAX_TILES_PER_WINDOW = 32
LARGE_WINDOW_SIZE = MAX_TILES_PER_WINDOW * (TILE_SIZE - OVERLAP) + OVERLAP
LARGE_WINDOW_OVERLAP = 16

# -------------------------
#  并发/硬件参数
# -------------------------
# 使用系统真实核心数；在某些 HPC 节点上 os.cpu_count() 可能返回 None，做保护
NUM_CORES = 64
MAX_RAM_GB = 700

# IO 线程数（主要用于 GeoTIFF 解压 & RasterIO read）
IO_THREADS = min(16, NUM_CORES)

CPU_COUNT = NUM_CORES  # 向后兼容旧常量名
CONCURRENT_THREADS = IO_THREADS  # 向后兼容旧常量名

# -------------------------
# 性能优化环境变量设置
# -------------------------
# 设置数值计算库线程数，避免线程竞争
os.environ.setdefault("OMP_NUM_THREADS", "1")        # OpenMP线程数
os.environ.setdefault("OPENBLAS_NUM_THREADS", "1")   # OpenBLAS线程数
os.environ.setdefault("MKL_NUM_THREADS", "1")        # Intel MKL线程数
os.environ.setdefault("NUMEXPR_NUM_THREADS", "1")    # NumExpr线程数

# -------------------------
# GDAL / RasterIO IO 优化
# -------------------------
# 通过环境变量让 GDAL 避免在打开文件时列出目录内容，并启用内部多线程读取。
# 这些设置对 GeoTIFF 等栅格文件读取的性能提升显著，尤其在高并发场景下。
os.environ.setdefault("GDAL_DISABLE_READDIR_ON_OPEN", "YES")  # 避免 ReadDir 阻塞
os.environ.setdefault("GDAL_NUM_THREADS", str(CPU_COUNT))       # GDAL 内部线程数

# 将 GDAL 缓存设置为系统内存的 50%（单位 MB）
try:
    total_mem_mb = int(psutil.virtual_memory().total / (1024 ** 2))
    os.environ["GDAL_CACHEMAX"] = str(max(512, int(total_mem_mb * 0.5)))
except Exception:
    # 回退到 1 GiB
    os.environ.setdefault("GDAL_CACHEMAX", "1024")

# 扩大一次性扫描大小，顺序读取更快
os.environ.setdefault("GDAL_SWATH_SIZE", "512M")

# 在程序启动时全局启用 rasterio.Env，使上述 GDAL 配置立即生效
_rio_env = rasterio.Env()
_rio_env.__enter__()

# 确保程序结束时正确退出 rasterio.Env，释放资源
atexit.register(_rio_env.__exit__, None, None, None)

# 定义程序退出时的清理函数
def _cleanup_resources():
    """程序退出时清理资源"""
    # 关闭所有线程的dataset缓存
    for thread_id, thread in threading._active.items():
        if hasattr(thread, "ds_cache"):
            try:
                thread.ds_cache.close_all()
            except Exception:
                pass

    # 显式触发一次全局垃圾回收
    gc.collect()

# 注册程序退出时的清理函数
atexit.register(_cleanup_resources)

def is_valid_geotiff(filename):
    """检查GeoTIFF文件有效性"""
    try:
        stat_info = os.stat(filename)
        return stat_info.st_size >= 1024
    except:
        return False

def extract_date_from_files(filename):
    """从GLAD文件名提取日期
    GLAD文件名格式：2019_11_percent.tif
    """
    try:
        basename = os.path.basename(filename)
        # 移除.tif后缀
        name_parts = basename.replace('.tif', '').split('_')

        if len(name_parts) >= 2:
            year = name_parts[0]
            month = name_parts[1]

            if year in VALID_YEARS and month in VALID_MONTHS:
                return year, month

        return False, False
    except Exception as e:
        logger.warning(f"Error extracting date from {filename}: {e}")
        return False, False

def get_image_metadata(filename):
    """获取图像元数据"""
    try:
        with rasterio.open(filename) as src:
            lon, lat = src.transform * (0, 0)
            # 确保transform只包含6个参数 (a, b, c, d, e, f)
            transform = src.transform
            transform_list = [transform.a, transform.b, transform.c,
                            transform.d, transform.e, transform.f]
            return {
                'lon': round(lon, 0),
                'lat': round(lat, 0),
                'width': src.width,
                'height': src.height,
                'bounds': src.bounds,
                'transform': transform_list,
                'crs': src.crs
            }
    except Exception as e:
        logger.error(f"Failed to read metadata: {filename} ({str(e)})")
        return None

def extract_lon_lat_from_dirname(dirname):
    """从GLAD目录名提取经纬度
    目录名格式：80N_170W 或 80S_170E
    """
    try:
        # 分离纬度和经度部分
        parts = dirname.split('_')
        if len(parts) != 2:
            return None, None

        lat_str, lon_str = parts[0], parts[1]

        # 解析纬度
        lat_val = int(lat_str[:-1])
        lat_dir = lat_str[-1]
        lat = lat_val if lat_dir == 'N' else -lat_val

        # 解析经度
        lon_val = int(lon_str[:-1])
        lon_dir = lon_str[-1]
        lon = lon_val if lon_dir == 'E' else -lon_val

        return lon, lat
    except Exception as e:
        logger.warning(f"Error extracting lon/lat from {dirname}: {e}")
        return None, None

def find_lon_lat_groups(glad_dir, processes=16, glad_metadata_file=None):
    """查找并分组GLAD文件"""

    # 收集GLAD文件
    glad_files = []

    # 遍历GLAD目录结构
    for subdir in os.listdir(glad_dir):
        subdir_path = os.path.join(glad_dir, subdir)
        if not os.path.isdir(subdir_path):
            continue

        # 从目录名提取经纬度
        lon, lat = extract_lon_lat_from_dirname(subdir)
        if lon is None or lat is None:
            continue

        # 查找该目录下的所有tif文件
        tif_files = glob.glob(os.path.join(subdir_path, "*.tif"))
        tif_files = [f for f in tif_files if is_valid_geotiff(f)]

        for f in tif_files:
            # 只处理percent文件
            if 'percent' in f:
                glad_files.append(f)

    logger.info(f"Found {len(glad_files)} GLAD percent files")

    # 加载或生成元数据
    if glad_metadata_file and os.path.exists(glad_metadata_file):
        with open(glad_metadata_file, 'r') as f:
            glad_metadata = json.load(f)
    else:
        with ThreadPoolExecutor(max_workers=processes) as executor:
            results = list(executor.map(get_image_metadata, glad_files))
        glad_metadata = {file: meta for file, meta in zip(glad_files, results) if meta}

    # 按经纬度分组
    lon_lat_groups = {}
    for glad_file in glad_files:
        year, month = extract_date_from_files(glad_file)
        if not year:
            continue

        metadata = glad_metadata.get(glad_file)
        if not metadata:
            continue

        lon, lat = metadata['lon'], metadata['lat']
        lon_lat = f"{lon}_{lat}"

        if lon_lat not in lon_lat_groups:
            lon_lat_groups[lon_lat] = []

        # GLAD没有occurrence文件，所以第二个参数为None
        lon_lat_groups[lon_lat].append((glad_file, None, year, month))

    return lon_lat_groups, glad_metadata

def calculate_large_window_coords(width, height):
    """计算大窗口坐标"""
    step = LARGE_WINDOW_SIZE - LARGE_WINDOW_OVERLAP
    x_coords = np.arange(0, width, step, dtype=np.int32)
    y_coords = np.arange(0, height, step, dtype=np.int32)
    return x_coords, y_coords

def calculate_tile_coords(src_width, src_height, tile_size=TILE_SIZE, overlap=OVERLAP):
    """计算tile坐标，参考原始calculate_tile_window函数的简洁实现"""
    step = tile_size - overlap

    # 计算所有可能的tile起始坐标
    x_coords = np.arange(0, src_width - tile_size + 1, step, dtype=np.int32)
    x_coords = np.append(x_coords, max(src_width - tile_size, 0))
    x_coords = np.unique(x_coords)  # 确保唯一性

    y_coords = np.arange(0, src_height - tile_size + 1, step, dtype=np.int32)
    y_coords = np.append(y_coords, max(src_height - tile_size, 0))
    y_coords = np.unique(y_coords)

    return x_coords, y_coords

def calculate_tiles_in_large_window(window_width=LARGE_WINDOW_SIZE, window_height=LARGE_WINDOW_SIZE):
    """计算大窗口内的tile坐标 - 基于传入的窗口尺寸"""
    return calculate_tile_coords(window_width, window_height)

def find_adjacent_images(current_lon, current_lat, lon_lat_groups, tile_spacing=10):
    """根据lon_lat坐标查找邻近图像，处理经纬度跨界情况"""
    adjacent_images = {}

    # 定义邻近图像的相对位置（通常JRC图像间距为10度）
    directions = {
        'north': (0, tile_spacing),
        'south': (0, -tile_spacing),
        'east': (tile_spacing, 0),
        'west': (-tile_spacing, 0),
        'northeast': (tile_spacing, tile_spacing),
        'northwest': (-tile_spacing, tile_spacing),
        'southeast': (tile_spacing, -tile_spacing),
        'southwest': (-tile_spacing, -tile_spacing)
    }

    for direction, (lon_offset, lat_offset) in directions.items():
        adj_lon = current_lon + lon_offset
        adj_lat = current_lat + lat_offset

        # 处理经度环绕（-180到180度）
        if adj_lon > 180:
            adj_lon = adj_lon - 360
        elif adj_lon < -180:
            adj_lon = adj_lon + 360

        # 处理纬度越界（超出-90到90度范围的情况）
        # 注意：JRC数据通常不会到达极地，但为了完整性还是处理
        if adj_lat > 90 or adj_lat < -90:
            # 跳过无效的纬度
            logger.debug(f"Skipping {direction}: invalid latitude {adj_lat}")
            continue

        # 尝试两种可能的键格式（处理浮点数精度问题）
        adj_key = f"{int(adj_lon)}_{int(adj_lat)}"
        adj_key_rounded = f"{int(round(adj_lon))}_{int(round(adj_lat))}"

        if adj_key in lon_lat_groups:
            adjacent_images[direction] = lon_lat_groups[adj_key]
            logger.debug(f"Found adjacent image {direction}: {adj_key}")
        elif adj_key_rounded in lon_lat_groups and adj_key_rounded != adj_key:
            # 尝试四舍五入的版本
            adjacent_images[direction] = lon_lat_groups[adj_key_rounded]
            logger.debug(f"Found adjacent image {direction}: {adj_key_rounded} (rounded)")

    return adjacent_images

def read_time_series_data_concurrent(time_items, window, adjacent_images, window_col_off, window_row_off,
                                   window_width, window_height, src_width, src_height, needs_adjacent_data,
                                   max_workers=CONCURRENT_THREADS):
    """并发读取时间序列数据，提升IO效率（GLAD版本）"""
    data_cache = {}

    def read_single_time_step(time_key, glad_file, _, year, month):  # _表示occ_file，GLAD中不使用
        """读取单个时间步的数据"""
        try:
            src = get_dataset(glad_file)
            glad_data = read_large_window_with_adjacent(
                src, window, adjacent_images,
                window_col_off, window_row_off, window_width, window_height,
                src_width, src_height, time_key, needs_adjacent_data
            )

            if glad_data is not None:
                return time_key, {
                    'glad_data': glad_data,
                    'year': year,
                    'month': month
                }
            return None, None

        except Exception as e:
            logger.error(f"Error reading time step {time_key} from {glad_file}: {e}")
            return None, None

    # 使用线程池并发读取
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有读取任务
        future_to_time = {
            executor.submit(read_single_time_step, time_key, glad_file, occ_file, year, month): time_key
            for time_key, (glad_file, occ_file, year, month) in time_items.items()
        }

        # 收集结果
        for future in as_completed(future_to_time):
            time_key, time_data = future.result()
            if time_key is not None and time_data is not None:
                data_cache[time_key] = time_data

    logger.debug(f"Successfully read {len(data_cache)}/{len(time_items)} time steps")
    return data_cache

def read_occurrence_data_concurrent(occ_files, window, adjacent_images, window_col_off, window_row_off,
                                  window_width, window_height, src_width, src_height, needs_adjacent_data):
    """并发读取occurrence数据"""
    if not occ_files:
        return None

    def read_single_occurrence(occ_file):
        """读取单个occurrence文件"""
        try:
            src = get_dataset(occ_file)
            return read_large_window_with_adjacent(
                src, window, adjacent_images,
                window_col_off, window_row_off, window_width, window_height,
                src_width, src_height, None, needs_adjacent_data, is_occurrence=True
            )
        except Exception as e:
            logger.error(f"Error reading occurrence file {occ_file}: {e}")
            return None

    # 对于occurrence文件，通常只有一个，但保持一致的接口
    first_occ_file = next(iter(occ_files))
    return read_single_occurrence(first_occ_file)

def read_large_window_with_adjacent(src, window, adjacent_images, window_col_off, window_row_off,
                                  window_width, window_height, src_width, src_height,
                                  time_key, needs_adjacent_data, is_occurrence=False):
    """读取大窗口数据，必要时从邻近图像补充"""
    try:
        # 始终创建完整大小的数据数组（255为GLAD无数据值）
        extended_data = np.full((window_height, window_width), GLAD_NODATA, dtype=np.uint8)

        # 计算在原始图像中可以读取的实际区域
        actual_width = min(window_width, src_width - window_col_off)
        actual_height = min(window_height, src_height - window_row_off)

        if actual_width > 0 and actual_height > 0:
            # 读取原始图像中的有效部分
            actual_window = Window(window_col_off, window_row_off, actual_width, actual_height)
            original_data = src.read(1, window=actual_window).astype(np.uint8)
            # 将原始数据复制到扩展数组的左上角
            extended_data[:actual_height, :actual_width] = original_data

        # 检查需要从哪些方向补充数据
        needs_east = window_col_off + window_width > src_width
        needs_south = window_row_off + window_height > src_height

        # 如果需要邻近数据且有邻近图像，则读取补充数据
        if (needs_east or needs_south) and adjacent_images:
            # 确定需要读取的邻近图像方向
            directions_to_read = []
            if needs_east and needs_south:
                directions_to_read = ['east', 'south', 'southeast']
            elif needs_east:
                directions_to_read = ['east']
            elif needs_south:
                directions_to_read = ['south']

            for direction in directions_to_read:
                if direction in adjacent_images:
                    adj_data = read_adjacent_image_data(
                        adjacent_images[direction], direction, window_col_off, window_row_off,
                        window_width, window_height, src_width, src_height, time_key, is_occurrence
                    )

                    if adj_data is not None:
                        # 将邻近数据合并到扩展数组中
                        merge_adjacent_to_extended(
                            extended_data, adj_data, direction,
                            window_col_off, window_row_off, src_width, src_height
                        )

        return extended_data

    except Exception as e:
        logger.error(f"Error reading large window with adjacent data: {e}")
        return src.read(1, window=window).astype(np.uint8)

def read_adjacent_image_data(adjacent_group, direction, window_col_off, window_row_off,
                           window_width, window_height, src_width, src_height, time_key, is_occurrence=False):
    """从邻近图像读取指定方向的数据"""
    try:
        # 查找对应时间的文件
        target_file = None
        if is_occurrence:
            # occurrence文件不依赖时间
            for jrc_file, occ_file, year, month in adjacent_group:
                if occ_file:
                    target_file = occ_file
                    break
        else:
            # JRC文件需要匹配时间
            if time_key:
                year, month = time_key.split('-')
                for jrc_file, occ_file, file_year, file_month in adjacent_group:
                    if file_year == year and file_month == month:
                        target_file = jrc_file
                        break

        if not target_file:
            return None

        # 计算在邻近图像中需要读取的区域
        if direction == 'east':
            # 读取邻近图像的左侧部分
            adj_col_start = 0
            adj_row_start = window_row_off
            adj_width = (window_col_off + window_width) - src_width
            adj_height = window_height
        elif direction == 'south':
            # 读取邻近图像的上侧部分
            adj_col_start = window_col_off
            adj_row_start = 0
            adj_width = window_width
            adj_height = (window_row_off + window_height) - src_height
        elif direction == 'southeast':
            # 读取邻近图像的左上角部分
            adj_col_start = 0
            adj_row_start = 0
            adj_width = (window_col_off + window_width) - src_width
            adj_height = (window_row_off + window_height) - src_height
        else:
            return None

        # 确保读取区域有效
        if adj_width <= 0 or adj_height <= 0:
            return None

        adj_window = Window(adj_col_start, adj_row_start, adj_width, adj_height)

        adj_src = get_dataset(target_file)
        adj_data = adj_src.read(1, window=adj_window).astype(np.uint8)
        logger.debug(f"Read adjacent data from {direction}: {target_file}, shape: {adj_data.shape}")
        return adj_data

    except Exception as e:
        logger.warning(f"Failed to read adjacent image data from {direction}: {e}")
        return None

def merge_adjacent_to_extended(extended_data, adj_data, direction, window_col_off, window_row_off, src_width, src_height):
    """将邻近图像数据合并到扩展数组中"""
    try:
        if direction == 'east':
            # 合并右侧数据
            start_col = src_width - window_col_off
            if start_col < extended_data.shape[1]:
                width = min(adj_data.shape[1], extended_data.shape[1] - start_col)
                height = min(adj_data.shape[0], extended_data.shape[0])
                extended_data[:height, start_col:start_col+width] = adj_data[:height, :width]

        elif direction == 'south':
            # 合并下侧数据
            start_row = src_height - window_row_off
            if start_row < extended_data.shape[0]:
                height = min(adj_data.shape[0], extended_data.shape[0] - start_row)
                width = min(adj_data.shape[1], extended_data.shape[1])
                extended_data[start_row:start_row+height, :width] = adj_data[:height, :width]

        elif direction == 'southeast':
            # 合并右下角数据
            start_col = src_width - window_col_off
            start_row = src_height - window_row_off
            if start_col < extended_data.shape[1] and start_row < extended_data.shape[0]:
                width = min(adj_data.shape[1], extended_data.shape[1] - start_col)
                height = min(adj_data.shape[0], extended_data.shape[0] - start_row)
                extended_data[start_row:start_row+height, start_col:start_col+width] = adj_data[:height, :width]

        logger.debug(f"Successfully merged {direction} data into extended array")

    except Exception as e:
        logger.error(f"Error merging {direction} data: {e}")

def calculate_tile_window(window_width, window_height, idx_x, idx_y, tile_size=TILE_SIZE, overlap=OVERLAP):
    """计算tile窗口，参考原始函数的简洁实现"""
    step = tile_size - overlap

    # 计算所有可能的tile起始坐标
    x_coords = np.arange(0, window_width - tile_size + 1, step, dtype=np.int32)
    x_coords = np.append(x_coords, max(window_width - tile_size, 0))
    x_coords = np.unique(x_coords)

    y_coords = np.arange(0, window_height - tile_size + 1, step, dtype=np.int32)
    y_coords = np.append(y_coords, max(window_height - tile_size, 0))
    y_coords = np.unique(y_coords)

    if idx_x >= len(x_coords) or idx_y >= len(y_coords):
        logger.warning(f"Tile index ({idx_x}, {idx_y}) out of bounds for window {window_width}x{window_height}")
        return None

    col_off = x_coords[idx_x]
    row_off = y_coords[idx_y]

    # rasterio会自动处理边界外的读取，使用nodata填充
    window = Window(col_off, row_off, tile_size, tile_size)

    return window

@contextmanager
def open_rasterio_files(*files):
    """安全打开rasterio文件"""
    datasets = []
    try:
        datasets = [rasterio.open(f) for f in files if f]
        yield datasets
    finally:
        for ds in datasets:
            ds.close()

def process_data(glad_data):
    """处理GLAD数据 - 保留原始0-100水体概率值"""
    try:
        # 保持原始数据类型和值，不做任何转换
        # GLAD数据格式：0-100为水体概率值，255为无数据值
        if glad_data.dtype != np.uint8:
            data = glad_data.astype(np.uint8, copy=False)
        else:
            data = glad_data  # 保持原始数据不变

        # 统计像素分布（保持原始值范围0-100，255为无数据）
        total_pixels = data.size
        water_pixels = np.count_nonzero((data >= WATER_THRESHOLD) & (data <= GLAD_WATER_MAX))  # 0-100范围内的像素
        nodata_pixels = np.count_nonzero(data == GLAD_NODATA)  # 无数据像素

        water_proportion = water_pixels / total_pixels if total_pixels else 0.0
        missing_proportion = nodata_pixels / total_pixels if total_pixels else 0.0

        return data, water_proportion, missing_proportion
    except Exception as e:
        logger.error(f"Error processing data: {str(e)}")
        return None, 0.0, 0.0

def process_tile_from_cache(args):
    """从预分配的tile数据处理单个tile"""
    tile_idx_x, tile_idx_y, tile_data_dict, global_time_coords = args

    try:
        # GLAD数据没有occurrence data，跳过相关检查

        # 按照全局时间坐标对齐数据
        num_time_steps = len(global_time_coords)
        data_arrays = np.full((num_time_steps, TILE_SIZE, TILE_SIZE), GLAD_NODATA, dtype=np.uint8)  # 255为GLAD无数据值
        water_proportions = np.full(num_time_steps, np.nan, dtype=np.float32)
        missing_proportions = np.full(num_time_steps, np.nan, dtype=np.float32)

        # 处理时间序列数据，按全局时间坐标对齐
        for time_key, time_data in tile_data_dict['time_data'].items():
            tile_glad_data = time_data['tile_glad_data']
            year, month = time_data['year'], time_data['month']
            time_coord = f"{year}-{int(month):02d}-01"

            # 找到对应的时间索引
            try:
                time_idx = global_time_coords.index(time_coord)
            except ValueError:
                logger.warning(f"Time coordinate {time_coord} not found in global time coords")
                continue

            # 确保数据形状正确
            if tile_glad_data.shape != (TILE_SIZE, TILE_SIZE):
                logger.warning(f"Incomplete GLAD data for tile {tile_idx_x}_{tile_idx_y}, time {time_key}: {tile_glad_data.shape}")
                continue

            processed_data, water_proportion, missing_proportion = process_data(tile_glad_data)

            if processed_data is not None:
                data_arrays[time_idx] = processed_data
                water_proportions[time_idx] = water_proportion
                missing_proportions[time_idx] = missing_proportion

        # 先计算occ_data，然后基于occ_data计算平均水体频率
        # 这样确保mean_water_frequency和occ_data的计算逻辑一致

        # 计算整个时间序列的水体频率 - 向量化实现
        # 水体频率 = 有水体的观测次数 / 总的有效观测次数

        # 向量化计算有效观测次数（所有像素，所有时间步）
        # data_arrays shape: (num_time_steps, TILE_SIZE, TILE_SIZE)
        valid_mask = (data_arrays != GLAD_NODATA)  # shape: (num_time_steps, TILE_SIZE, TILE_SIZE)
        valid_observations = np.sum(valid_mask, axis=0)  # shape: (TILE_SIZE, TILE_SIZE)

        # 向量化计算水体观测次数（值 > 0 且 <= 100的次数）
        # 注意：0表示无水体，1-100表示有水体（不同概率）
        water_mask = (data_arrays >= WATER_THRESHOLD) & (data_arrays <= GLAD_WATER_MAX) & valid_mask
        water_observations = np.sum(water_mask, axis=0)  # shape: (TILE_SIZE, TILE_SIZE)

        # 向量化计算水体频率，使用uint8类型，范围0-100，255表示无数据
        occ_data_array = np.full((TILE_SIZE, TILE_SIZE), GLAD_NODATA, dtype=np.uint8)

        # 只对有有效观测的像素计算频率，转换为0-100的整数范围
        valid_pixels_mask = valid_observations > 0
        if np.any(valid_pixels_mask):
            frequency_float = (
                water_observations[valid_pixels_mask].astype(np.float32) /
                valid_observations[valid_pixels_mask].astype(np.float32)
            )
            # 转换为0-100的uint8范围
            frequency_uint8 = np.round(frequency_float * 100).astype(np.uint8)
            occ_data_array[valid_pixels_mask] = frequency_uint8

        # 如果全部为无数据，跳过该tile；允许全部为0或全部为100的有效情况
        if np.all(occ_data_array == GLAD_NODATA):
            logger.debug(f"All pixels in tile {tile_idx_x}_{tile_idx_y} are NoData")
            return None

        # 基于occ_data计算mean_water_frequency，保持0.0-1.0范围以兼容系统其他部分
        valid_occ_mask = occ_data_array != GLAD_NODATA
        if np.any(valid_occ_mask):
            # 将occ_data (0-100) 转换回 (0.0-1.0) 范围来计算平均值
            mean_water_frequency = float(np.mean(occ_data_array[valid_occ_mask]) / 100.0)
        else:
            mean_water_frequency = np.nan

        return {
            'idx_x': tile_idx_x,
            'idx_y': tile_idx_y,
            'data_arrays': data_arrays,
            'water_proportions': water_proportions,
            'missing_proportions': missing_proportions,
            'mean_water_frequency': mean_water_frequency,
            'occ_data': occ_data_array,  # 新增：水体频率数组
            'tile_col_offset': tile_data_dict['tile_col_offset'],
            'tile_row_offset': tile_data_dict['tile_row_offset'],
        }

    except Exception as e:
        logger.error(f"Error processing tile ({tile_idx_x}, {tile_idx_y}): {e}")
        return None

def prepare_tile_data_chunks(data_cache, tile_coords):
    """预先为每个tile准备数据块，减少并行进程间的数据传输
    注意：GLAD数据没有occurrence data
    """
    x_coords, y_coords = tile_coords
    tile_data_chunks = {}

    logger.debug(f"Preparing data chunks for {len(x_coords)}x{len(y_coords)} tiles")

    for tile_idx_x in range(len(x_coords)):
        for tile_idx_y in range(len(y_coords)):
            tile_col = x_coords[tile_idx_x]
            tile_row = y_coords[tile_idx_y]

            # 为当前tile准备数据字典
            tile_data_dict = {
                'tile_col_offset': tile_col,
                'tile_row_offset': tile_row,
                'time_data': {},
            }

            # 提取每个时间步的GLAD数据 - 始终确保为TILE_SIZE x TILE_SIZE
            for time_key, time_data in data_cache.items():
                glad_data = time_data['glad_data']
                year, month = time_data['year'], time_data['month']

                # 提取tile数据
                tile_glad_data = glad_data[tile_row:tile_row+TILE_SIZE, tile_col:tile_col+TILE_SIZE]

                # 确保数据尺寸正确
                if tile_glad_data.shape == (TILE_SIZE, TILE_SIZE):
                    final_glad_data = tile_glad_data
                else:
                    # 只在需要时创建填充数组（255为GLAD无数据值）
                    padded_glad = np.full((TILE_SIZE, TILE_SIZE), GLAD_NODATA, dtype=np.uint8)
                    logger.warning(f"Incomplete GLAD data for tile {tile_idx_x}_{tile_idx_y}, time {time_key}: {tile_glad_data.shape}")
                    h, w = tile_glad_data.shape
                    if h > 0 and w > 0:
                        padded_glad[:h, :w] = tile_glad_data
                    final_glad_data = padded_glad

                tile_data_dict['time_data'][time_key] = {
                    'tile_glad_data': final_glad_data,
                    'year': year,
                    'month': month
                }

            tile_data_chunks[(tile_idx_x, tile_idx_y)] = tile_data_dict

    return tile_data_chunks

def create_netcdf_file(output_file, window_attrs, tile_results, time_coords):
    """创建完整的NetCDF文件（GLAD版本，无occurrence数据）"""
    try:
        if not tile_results:
            logger.warning(f"No tile results to write for {output_file}")
            return

        # 确定维度
        max_idx_x = max(result['idx_x'] for result in tile_results) + 1
        max_idx_y = max(result['idx_y'] for result in tile_results) + 1
        num_time_steps = len(time_coords)

        # 创建坐标
        coords = {
            'idx_x': np.arange(max_idx_x),
            'idx_y': np.arange(max_idx_y),
            'time': pd.to_datetime(time_coords),
            'y': np.arange(TILE_SIZE),
            'x': np.arange(TILE_SIZE)
        }

        # 初始化数据数组（255为GLAD无数据值）
        data_array = np.full((max_idx_x, max_idx_y, num_time_steps, TILE_SIZE, TILE_SIZE),
                            GLAD_NODATA, dtype=np.uint8)
        water_prop_array = np.full((max_idx_x, max_idx_y, num_time_steps), np.nan, dtype=np.float32)
        missing_prop_array = np.full((max_idx_x, max_idx_y, num_time_steps), np.nan, dtype=np.float32)
        mean_freq_array = np.full((max_idx_x, max_idx_y), np.nan, dtype=np.float32)
        col_offset_array = np.zeros((max_idx_x, max_idx_y), dtype=np.int32)
        row_offset_array = np.zeros((max_idx_x, max_idx_y), dtype=np.int32)

        # 新增：为每个tile保存具体的经纬度坐标
        tile_lon_array = np.full((max_idx_x, max_idx_y), np.nan, dtype=np.float32)
        tile_lat_array = np.full((max_idx_x, max_idx_y), np.nan, dtype=np.float32)

        # 新增：水体频率数组 - 每个像素的时间序列水体频率，使用uint8类型，范围0-100，255表示无数据
        occ_data_array = np.full((max_idx_x, max_idx_y, TILE_SIZE, TILE_SIZE),
                                GLAD_NODATA, dtype=np.uint8)

        # 获取窗口的地理变换信息
        # 确保只使用前6个参数来创建Affine对象
        transform_params = window_attrs['window_transform']
        if len(transform_params) > 6:
            transform_params = transform_params[:6]  # 只取前6个参数
        window_transform = rasterio.transform.Affine(*transform_params)

        # 使用实际的窗口尺寸计算tile偏移
        actual_window_width = window_attrs.get('window_width', LARGE_WINDOW_SIZE)
        actual_window_height = window_attrs.get('window_height', LARGE_WINDOW_SIZE)

        # 计算tile坐标（基于实际窗口大小）
        step = TILE_SIZE - OVERLAP
        x_coords = np.arange(0, actual_window_width - TILE_SIZE + 1, step, dtype=np.int32)
        x_coords = np.append(x_coords, max(actual_window_width - TILE_SIZE, 0))
        x_coords = np.unique(x_coords)

        y_coords = np.arange(0, actual_window_height - TILE_SIZE + 1, step, dtype=np.int32)
        y_coords = np.append(y_coords, max(actual_window_height - TILE_SIZE, 0))
        y_coords = np.unique(y_coords)

        # 填充所有可能的偏移值和坐标（tile在实际窗口内的偏移）
        for idx_x in range(min(max_idx_x, len(x_coords))):
            for idx_y in range(min(max_idx_y, len(y_coords))):
                col_offset_array[idx_x, idx_y] = x_coords[idx_x]  # tile在window内的偏移
                row_offset_array[idx_x, idx_y] = y_coords[idx_y]  # tile在window内的偏移

                # 计算每个tile的中心点经纬度坐标
                tile_center_col = x_coords[idx_x] + TILE_SIZE // 2
                tile_center_row = y_coords[idx_y] + TILE_SIZE // 2
                tile_lon, tile_lat = window_transform * (tile_center_col, tile_center_row)
                tile_lon_array[idx_x, idx_y] = tile_lon
                tile_lat_array[idx_x, idx_y] = tile_lat

        # 填充数据（数据已经按时间坐标对齐，无需额外匹配）
        for result in tile_results:
            idx_x, idx_y = result['idx_x'], result['idx_y']

            # 直接填充已对齐的数据
            data_array[idx_x, idx_y, :, :, :] = result['data_arrays']
            water_prop_array[idx_x, idx_y, :] = result['water_proportions']
            missing_prop_array[idx_x, idx_y, :] = result['missing_proportions']

            mean_freq_array[idx_x, idx_y] = result['mean_water_frequency']
            col_offset_array[idx_x, idx_y] = result['tile_col_offset']
            row_offset_array[idx_x, idx_y] = result['tile_row_offset']

            # 新增：填充水体频率数据
            occ_data_array[idx_x, idx_y, :, :] = result['occ_data']

            # 重新计算tile的经纬度（基于实际的tile偏移）
            tile_center_col = result['tile_col_offset'] + TILE_SIZE // 2
            tile_center_row = result['tile_row_offset'] + TILE_SIZE // 2
            tile_lon, tile_lat = window_transform * (tile_center_col, tile_center_row)
            tile_lon_array[idx_x, idx_y] = tile_lon
            tile_lat_array[idx_x, idx_y] = tile_lat

        # 创建Dataset
        ds = xr.Dataset(
            {
                'data': (('idx_x', 'idx_y', 'time', 'y', 'x'), data_array),
                'water_proportion': (('idx_x', 'idx_y', 'time'), water_prop_array),
                'missing_proportion': (('idx_x', 'idx_y', 'time'), missing_prop_array),
                'mean_water_frequency': (('idx_x', 'idx_y'), mean_freq_array),
                'occ_data': (('idx_x', 'idx_y', 'y', 'x'), occ_data_array),  # 新增：水体频率
                'tile_col_offset': (('idx_x', 'idx_y'), col_offset_array),
                'tile_row_offset': (('idx_x', 'idx_y'), row_offset_array),
                'tile_lon': (('idx_x', 'idx_y'), tile_lon_array),  # 新增：每个tile的经度
                'tile_lat': (('idx_x', 'idx_y'), tile_lat_array),  # 新增：每个tile的纬度
            },
            coords=coords
        )

        # 设置属性
        ds.attrs = {
            'Conventions': 'CF-1.8',
            'history': f"Created on {pd.Timestamp.now().strftime('%Y-%m-%d')}",
            'source': 'Data processed from GLAD large windows',
            'title': 'GLAD Surface Water Classification Large Window Tiles',
            'total_tiles_processed': len(tile_results),
            'tile_size': TILE_SIZE,
            'tile_overlap': OVERLAP,
            'large_window_size': LARGE_WINDOW_SIZE,
            'max_tiles_per_window': MAX_TILES_PER_WINDOW,
            **window_attrs
        }

        # 保存文件
        encoding = {
            'data': {'chunksizes': (1, 1, 1, TILE_SIZE, TILE_SIZE), 'zlib': True, 'complevel': 3},
            'water_proportion': {'chunksizes': (1, 1, 1), 'zlib': True, 'complevel': 3},
            'missing_proportion': {'chunksizes': (1, 1, 1), 'zlib': True, 'complevel': 3},
            'mean_water_frequency': {'chunksizes': (1, 1), 'zlib': True, 'complevel': 3},
            'occ_data': {'chunksizes': (1, 1, TILE_SIZE, TILE_SIZE), 'zlib': True, 'complevel': 3},  # uint8类型，0-100范围，255=无数据
            'tile_col_offset': {'chunksizes': (1, 1), 'zlib': True, 'complevel': 3},
            'tile_row_offset': {'chunksizes': (1, 1), 'zlib': True, 'complevel': 3},
            'tile_lon': {'chunksizes': (1, 1), 'zlib': True, 'complevel': 3},  # 新增
            'tile_lat': {'chunksizes': (1, 1), 'zlib': True, 'complevel': 3},  # 新增
        }

        ds.to_netcdf(output_file, encoding=encoding, engine='netcdf4')
        # ---------------  内存清理 ---------------
        # 显式关闭 Dataset 并删除可能占用大量内存的对象，避免在多进程长时间运行中造成内存累积
        try:
            ds.close()
        except Exception:
            pass  # 避免因为关闭失败而中断流程

        # 删除显式创建的 numpy 数组，便于及时释放内存
        del ds, data_array, water_prop_array, missing_prop_array, mean_freq_array
        del col_offset_array, row_offset_array, tile_lon_array, tile_lat_array, occ_data_array

        # 触发 Python 垃圾回收
        gc.collect()
        time.sleep(1)
        logger.debug(f"Successfully created NetCDF file: {output_file}")

    except Exception as e:
        logger.error(f"Error creating NetCDF file {output_file}: {str(e)}")
        raise

class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, CRS):
            return obj.to_wkt()
        return super().default(obj)

def process_large_window(group, window_idx_x, window_idx_y, window_col_off, window_row_off,
                        window_width, window_height, original_image_meta, lon_lat_groups, processes=16,
                        src_width=None, src_height=None):
    """处理单个大窗口，支持邻近图像数据读取（GLAD版本）"""
    try:
        # 计算窗口内tile坐标（基于实际的窗口尺寸）
        tile_coords = calculate_tiles_in_large_window(window_width, window_height)
        x_coords, y_coords = tile_coords

        if len(x_coords) == 0 or len(y_coords) == 0:
            return None, None, None

        # 组织数据
        time_items = {}
        unique_glad_files = set()

        for glad_file, _, year, month in group:  # _表示occ_file，GLAD中为None
            time_key = f"{year}-{month}"
            time_items[time_key] = (glad_file, None, year, month)
            unique_glad_files.add(glad_file)

        # 获取源图像尺寸（优先使用传入参数，避免重复读取文件）
        glad_files = list(unique_glad_files)

        if src_width is None or src_height is None:
            with rasterio.open(glad_files[0]) as src:
                src_width, src_height = src.width, src.height

        # 查找邻近图像
        current_lon = original_image_meta['lon']
        current_lat = original_image_meta['lat']
        adjacent_images = find_adjacent_images(current_lon, current_lat, lon_lat_groups)

        # 创建窗口
        window = Window(window_col_off, window_row_off, window_width, window_height)

        # 检查大窗口是否超出原始图像边界
        needs_adjacent_data = (window_col_off + window_width > src_width or
                             window_row_off + window_height > src_height)

        # 使用16线程并发读取时间序列数据，提升IO效率
        logger.debug(f"Reading {len(time_items)} time steps with {CONCURRENT_THREADS} concurrent threads...")

        # 并发读取时间序列数据
        max_workers = min(CONCURRENT_THREADS, len(time_items))
        data_cache = read_time_series_data_concurrent(
            time_items, window, adjacent_images, window_col_off, window_row_off,
            window_width, window_height, src_width, src_height, needs_adjacent_data,
            max_workers=max_workers
        )

        # 计算全局时间坐标（统一排序，确保所有tile对齐）
        global_time_coords = sorted([f"{item[2]}-{int(item[3]):02d}-01" for item in group])

        # 预先为每个tile准备数据块，减少并行进程间的数据传输
        tile_data_chunks = prepare_tile_data_chunks(data_cache, tile_coords)

        # 清理大数据缓存以释放内存
        del data_cache
        gc.collect()

        time.sleep(5)
        # 准备并行处理参数 - 现在只传递每个tile需要的数据
        tasks = []
        for tile_idx_x in range(len(x_coords)):
            for tile_idx_y in range(len(y_coords)):
                tile_key = (tile_idx_x, tile_idx_y)
                if tile_key in tile_data_chunks:
                    args = (tile_idx_x, tile_idx_y, tile_data_chunks[tile_key], global_time_coords)
                    tasks.append(args)

        logger.info(f"Prepared {len(tasks)} tasks for parallel processing")

        # ------------------------------
        #  使用线程池并行处理 tile（零拷贝视图）
        # ------------------------------
        with ThreadPoolExecutor(max_workers=IO_THREADS) as executor:
            tile_results = list(executor.map(process_tile_from_cache, tasks))

        # 清理tile数据块
        del tile_data_chunks
        gc.collect()

        time.sleep(5)
        # 过滤有效结果
        valid_results = [r for r in tile_results if r is not None]

        if not valid_results:
            return None, None, None

        # 统计处理结果
        total_processed = len(tasks)
        logger.info(f"Window {window_idx_x}_{window_idx_y}: {len(valid_results)}/{total_processed} tiles valid")

        # 计算地理坐标
        # 确保只使用前6个参数构造Affine对象
        transform_params = original_image_meta['transform'][:6]
        base_transform = rasterio.transform.Affine(*transform_params)
        window_lon, window_lat = base_transform * (window_col_off, window_row_off)

        # 创建window的transform（从window左上角地理坐标开始）
        pixel_size_x = base_transform.a
        pixel_size_y = base_transform.e
        window_transform = Affine(pixel_size_x, 0, window_lon,
                                0, pixel_size_y, window_lat)

        window_attrs = {
            'window_transform': [window_transform.a, window_transform.b, window_transform.c,
                               window_transform.d, window_transform.e, window_transform.f],  # 只保存6个参数
            'window_width': window_width,  # 保存实际窗口宽度
            'window_height': window_height,  # 保存实际窗口高度
            'window_lon': window_lon,  # 保存window左上角经度，便于快速检索
            'window_lat': window_lat,  # 保存window左上角纬度，便于快速检索
            'window_col_offset': window_col_off,  # window在原始图像中的列偏移
            'window_row_offset': window_row_off,  # window在原始图像中的行偏移
        }

        # 返回结果，由上层函数负责写出 NetCDF 文件
        return valid_results, window_attrs, global_time_coords

    except Exception as e:
        logger.error(f"Error processing large window ({window_idx_x}, {window_idx_y}): {e}")
        return None, None, None

# ------------------------------
#  单窗口任务包装函数（供多进程池调用）
# ------------------------------


def _process_window_task(args):
    (
        group,
        window_idx_x,
        window_idx_y,
        window_col_off,
        window_row_off,
        window_width,
        window_height,
        original_image_meta,
        lon_lat_groups,
        processes,
        src_width,
        src_height,
        output_file,
    ) = args

    try:
        # 快速检查文件是否已存在，避免重复工作
        if os.path.exists(output_file):
            logger.info(f"Skip existing window {output_file}")
            return True

        tile_results, window_attrs, time_coords = process_large_window(
            group,
            window_idx_x,
            window_idx_y,
            window_col_off,
            window_row_off,
            window_width,
            window_height,
            original_image_meta,
            lon_lat_groups,
            processes,
            src_width,
            src_height,
        )

        if tile_results is None:
            return False

        create_netcdf_file(output_file, window_attrs, tile_results, time_coords)

        # 内存清理：删除大型对象并进行垃圾回收
        del tile_results, window_attrs, time_coords
        gc.collect()

        return True
    except Exception as e:
        logger.error(f"Window task failed: {e}")
        return False


def process_dataset(
    glad_dir,
    output_dir="output/glad_large_windows",
    processes=32,
):
    try:
        os.makedirs(output_dir, exist_ok=True)

        # 创建日志目录
        os.makedirs('logs', exist_ok=True)

        glad_metadata_file = os.path.join(output_dir, "glad_metadata.json")

        # 查找和分组文件
        lon_lat_groups, glad_metadata = find_lon_lat_groups(
            glad_dir, min(64, processes), glad_metadata_file
        )

        # 保存元数据
        if not os.path.exists(glad_metadata_file):
            with open(glad_metadata_file, "w") as f:
                json.dump(glad_metadata, f, cls=CustomJSONEncoder)

        # ------------------------------
        # 估算内存限制并创建全局进程池
        # ------------------------------
        est_per_window_gb = 30
        max_proc_by_ram = max(1, int(MAX_RAM_GB // est_per_window_gb))
        max_workers = min(NUM_CORES, max_proc_by_ram, processes)

        logger.info(
            f"Initializing ProcessPoolExecutor with {max_workers} workers (RAM limit {MAX_RAM_GB:.0f} GB)"
        )

        # 在外层创建一个进程池以复用worker
        with ProcessPoolExecutor(max_workers=max_workers) as global_pool:
            for lon_lat, group in tqdm(lon_lat_groups.items(), desc="Processing images"):
                # 每个影像都创建一个新的任务列表，避免累积
                tasks = []

                group = sorted(group, key=lambda x: (int(x[2]), int(x[3])))

                with rasterio.open(group[0][0]) as src:
                    width, height = src.width, src.height

                x_coords, y_coords = calculate_large_window_coords(width, height)

                logger.debug(f"Processing image {lon_lat} with {len(x_coords)}x{len(y_coords)} windows")

                original_image_meta = glad_metadata.get(group[0][0])
                if not original_image_meta:
                    logger.error(f"Missing metadata for {group[0][0]}")
                    continue

                for window_idx_x, window_col_off in enumerate(x_coords):
                    for window_idx_y, window_row_off in enumerate(y_coords):
                        # 确保只使用前6个参数构造Affine对象
                        transform_params = original_image_meta["transform"][:6]
                        base_transform = rasterio.transform.Affine(*transform_params)
                        window_lon, window_lat = base_transform * (window_col_off, window_row_off)

                        output_file = os.path.join(
                            output_dir,
                            f"GLAD_window_{window_lon:.3f}_{window_lat:.3f}.nc",
                        )

                        # 文件已存在检查移到任务函数内，这里只是跳过任务创建
                        if os.path.exists(output_file):
                            logger.info(f"Skip existing window {output_file}")
                            continue

                        # 为避免无法序列化 CRS，对元数据做浅拷贝并去除 'crs' 字段
                        meta_picklable = {k: v for k, v in original_image_meta.items() if k != "crs"}

                        task_args = (
                            group,
                            window_idx_x,
                            window_idx_y,
                            window_col_off,
                            window_row_off,
                            LARGE_WINDOW_SIZE,
                            LARGE_WINDOW_SIZE,
                            meta_picklable,
                            lon_lat_groups,
                            processes,
                            width,
                            height,
                            output_file,
                        )
                        tasks.append(task_args)

                logger.info(f"Submitting {len(tasks)} window tasks for image {lon_lat}")

                # 每个影像的任务单独处理
                if tasks:
                    list(global_pool.map(_process_window_task, tasks))

                # 清理线程局部存储的文件句柄缓存
                if hasattr(_thread_local, "ds_cache"):
                    _thread_local.ds_cache.close_all()
                    # 创建新的缓存实例
                    _thread_local.ds_cache = LRUCache(MAX_DS_CACHE_SIZE)

                # 强制垃圾回收
                gc.collect()
                time.sleep(1)
                logger.info(f"Completed processing image {lon_lat}")

            logger.info("All images processing completed")

    except Exception as e:
        logger.error(f"Error in process_dataset: {e}\n{traceback.format_exc()}")
        raise

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="GLAD大窗口数据处理工具")
    parser.add_argument('--glad_dir', type=str, default="/fossfs/xiaozhen/GLAD", help='GLAD data directory')
    parser.add_argument('--output_dir', type=str, default="/fossfs/xiaozhen/Clip/GLAD", help='Output directory for NetCDF files')
    parser.add_argument('--processes', type=int, default=16, help='Number of processes to use')
    args = parser.parse_args()

    process_dataset(
        glad_dir=args.glad_dir,
        output_dir=args.output_dir,
        processes=args.processes
    )