#!/bin/bash
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=32
#SBATCH --nodes=1
#SBATCH --account=geog_geors
#SBATCH --partition=c_foss_amd
#SBATCH --qos=normal
#SBATCH --time=100:00:00
#SBATCH --output=logs/clip_glad.out
#SBATCH --mem=700G
#SBATCH --mail-type=END
#SBATCH --mail-user=<EMAIL>

JRC_DIR=${1:-/fossfs/xiaozhen/GLAD}
OUT_DIR=${2:-/fossfs/xiaozhen/Clip/GLAD}
PROCESSES=${3:-32}

# Initialize Conda
source /home/<USER>/miniconda/bin/activate
conda activate water

python data/glad/clip_glad.py --glad_dir "$JRC_DIR" --output_dir "$OUT_DIR" --processes "$PROCESSES"