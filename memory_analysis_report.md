# 内存累积问题分析报告

## 概述
通过对代码的详细分析，发现了多个可能导致内存累积的问题。这些问题主要集中在文件句柄管理、大型数组处理、缓存机制和多进程/多线程环境下的资源清理。

## 主要问题分析

### 1. 线程局部缓存清理不彻底

**问题位置**: `data/glad/clip_glad.py` 和 `data/clip.py`

**问题描述**:
- 线程局部存储的 `_thread_local.ds_cache` 只在主线程中清理
- 子线程和进程池中的线程缓存可能未被正确清理
- `_cleanup_resources()` 函数试图访问所有线程，但在多进程环境下可能无效

**代码片段**:
```python
# 问题代码 - 只清理主线程的缓存
if hasattr(_thread_local, "ds_cache"):
    _thread_local.ds_cache.close_all()
    _thread_local.ds_cache = LRUCache(MAX_DS_CACHE_SIZE)
```

### 2. GDAL缓存设置过大

**问题位置**: `data/glad/clip_glad.py:252`

**问题描述**:
- GDAL缓存设置为系统内存的50%，在多进程环境下可能导致总内存使用超出系统限制
- 每个进程都会申请50%的系统内存作为GDAL缓存

**代码片段**:
```python
# 问题代码 - 每个进程都设置50%内存
os.environ["GDAL_CACHEMAX"] = str(max(512, int(total_mem_mb * 0.5)))
```

### 3. 大型数组延迟释放

**问题位置**: `data/glad/clip_glad.py` 多处

**问题描述**:
- 虽然使用了 `del` 和 `gc.collect()`，但在高并发环境下垃圾回收可能不及时
- `time.sleep()` 用于等待垃圾回收，但这是不可靠的方法
- 大型numpy数组在多进程间传递时可能产生额外拷贝

### 4. 文件句柄泄漏风险

**问题位置**: `get_dataset()` 函数

**问题描述**:
- 虽然有LRU缓存机制，但在异常情况下文件句柄可能未正确关闭
- 多线程环境下的文件句柄竞争可能导致泄漏

### 5. xarray Dataset 未显式关闭

**问题位置**: 多个文件中的数据加载代码

**问题描述**:
- 某些地方使用 `xr.open_dataset()` 但未使用上下文管理器
- Dataset对象可能持有底层文件句柄

## 具体内存累积场景

### 场景1: 多进程处理大量文件
1. 每个进程设置50%系统内存的GDAL缓存
2. 多个进程同时运行时总缓存超出系统内存
3. 线程局部缓存在进程间无法共享清理

### 场景2: 长时间运行的批处理
1. 文件句柄缓存逐渐累积
2. 大型数组在垃圾回收前占用内存
3. 异常处理不当导致资源未释放

### 场景3: 高并发IO操作
1. 线程池中的线程缓存未及时清理
2. rasterio数据集句柄在异常时可能泄漏
3. numpy数组在多线程间共享时产生额外内存占用

## 风险评估

### 高风险问题
1. **GDAL缓存设置过大** - 可能导致系统内存耗尽
2. **线程缓存清理不彻底** - 长期运行时累积大量文件句柄

### 中风险问题
1. **大型数组延迟释放** - 在内存紧张时可能导致OOM
2. **文件句柄泄漏** - 可能达到系统文件描述符限制

### 低风险问题
1. **xarray Dataset未显式关闭** - 通常由垃圾回收器处理

## 建议的修复方案

### 1. 优化GDAL缓存设置
```python
# 根据进程数调整GDAL缓存
max_processes = min(NUM_CORES, processes)
gdal_cache_per_process = max(128, int(total_mem_mb * 0.3 / max_processes))
os.environ["GDAL_CACHEMAX"] = str(gdal_cache_per_process)
```

### 2. 改进线程缓存清理
```python
# 在每个工作线程结束时清理缓存
def cleanup_thread_cache():
    if hasattr(_thread_local, "ds_cache"):
        _thread_local.ds_cache.close_all()
        delattr(_thread_local, "ds_cache")

# 在线程池任务结束时调用
```

### 3. 强制内存释放
```python
# 替换不可靠的sleep等待
def force_memory_cleanup():
    gc.collect()
    gc.collect()  # 调用两次确保清理
    if hasattr(gc, 'set_threshold'):
        gc.set_threshold(0)  # 临时禁用自动垃圾回收
        gc.collect()
        gc.set_threshold(700, 10, 10)  # 恢复默认设置
```

### 4. 使用上下文管理器
```python
# 确保文件句柄正确关闭
@contextmanager
def safe_open_dataset(path):
    ds = None
    try:
        ds = xr.open_dataset(path, decode_times=False, mask_and_scale=False)
        yield ds
    finally:
        if ds is not None:
            ds.close()
```

## 监控建议

1. **内存使用监控**: 添加内存使用情况的日志记录
2. **文件句柄监控**: 定期检查打开的文件描述符数量
3. **进程级监控**: 监控每个进程的内存使用情况

### 6. 大型数组重复分配

**问题位置**: `data/glad/clip_glad.py:939-953`, `data/clip.py:804-812`

**问题描述**:
- 在 `create_netcdf_file()` 函数中创建多个大型numpy数组
- 这些数组可能同时占用大量内存，特别是在处理大窗口时
- 数组大小为 `(max_idx_x, max_idx_y, num_time_steps, TILE_SIZE, TILE_SIZE)`

**代码片段**:
```python
# 问题代码 - 同时分配多个大型数组
data_array = np.full((max_idx_x, max_idx_y, num_time_steps, TILE_SIZE, TILE_SIZE), GLAD_NODATA, dtype=np.uint8)
water_prop_array = np.full((max_idx_x, max_idx_y, num_time_steps), np.nan, dtype=np.float32)
missing_prop_array = np.full((max_idx_x, max_idx_y, num_time_steps), np.nan, dtype=np.float32)
occ_data_array = np.full((max_idx_x, max_idx_y, TILE_SIZE, TILE_SIZE), GLAD_NODATA, dtype=np.uint8)
```

### 7. memmap文件清理不彻底

**问题位置**: `prediction/inference.py:1274-1280`

**问题描述**:
- memmap文件在使用后尝试删除，但删除失败时只是忽略异常
- 可能导致临时文件累积，占用磁盘空间
- memmap对象删除后可能仍有文件句柄未释放

### 8. 多进程间数据传递开销

**问题位置**: `data/glad/clip_glad.py:1338-1353`

**问题描述**:
- 大型数据结构（如 `lon_lat_groups`）在多进程间传递
- 每个进程都会收到完整的数据副本，造成内存浪费
- 进程池中的任务参数包含大量数据

## 内存使用量估算

### 单个大窗口的内存占用
假设一个大窗口包含32x32个tiles，每个tile为256x256像素，时间序列长度为100：

1. **主数据数组**: 32 × 32 × 100 × 256 × 256 × 1字节 ≈ 21 GB
2. **辅助数组**: 约 2-3 GB
3. **处理过程中的临时数组**: 约 5-10 GB
4. **总计**: 约 28-34 GB per 大窗口

### 多进程环境下的内存放大
- 如果同时处理4个大窗口：28-34 GB × 4 = 112-136 GB
- 加上GDAL缓存（每进程50%系统内存）：可能超过系统总内存

## 总结

当前代码存在多个内存累积风险点，主要集中在：

### 高优先级问题（立即修复）
1. **GDAL缓存配置不当** - 可能导致系统内存耗尽
2. **大型数组同时分配** - 在大窗口处理时可能导致OOM
3. **线程局部缓存清理不彻底** - 长期运行时累积大量文件句柄

### 中优先级问题（计划修复）
1. **多进程数据传递开销** - 造成内存浪费
2. **memmap文件清理不彻底** - 可能导致磁盘空间不足
3. **大型对象生命周期管理** - 在内存紧张时可能导致问题

### 低优先级问题（监控即可）
1. **xarray Dataset未显式关闭** - 通常由垃圾回收器处理

建议优先修复GDAL缓存设置、大型数组分配策略和线程缓存清理问题，这些是最可能导致内存累积和系统崩溃的根本原因。
