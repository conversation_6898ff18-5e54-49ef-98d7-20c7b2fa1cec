#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Compute monthly water area from restored TIFs and plot monthly changes.

Assumptions and notes:
- TIFs are produced by prediction/restoration.py with JRC encoding:
  land=1, water=2, nodata=255 (see JRC_VALUES below).
- Files are typically named like: mosaic_YYYYMMDD_HHMM.tif (time derived from NetCDF).
- Output directory for restored TIFs depends on how restoration was run:
  * If using EnhancedNetCDFToTIFRestorer directly: files are saved under the given output_dir
    with names like 'mosaic_YYYYMMDD_HHMM.tif'.
  * In older inference flow, restored tifs may be in <output_dir>/restored_tifs/.

This script:
- Scans a directory recursively for TIFs matching a glob pattern (default: mosaic_*.tif)
- Parses timestamps from filenames to derive month
- Computes water area per file considering CRS:
  * For geographic CRS (lat/lon, e.g., EPSG:4326): uses a spherical-geodesic per-row pixel area
    formula: A = R^2 * dλ * (sin φ2 - sin φ1)
  * For projected CRS (meter units): per-pixel area is |det(affine)| (square meters)
- Aggregates per month and saves CSV + line plot

Usage example:
    python prediction/monthly_water_area.py \
        --tif_dir /path/to/restored_tifs \
        --pattern 'mosaic_*.tif' \
        --out_dir /path/to/output

Outputs:
- monthly_water_area.csv (columns: month, water_area_km2)
- monthly_water_area.png (line chart)
"""
from __future__ import annotations

import argparse
import math
import re
from pathlib import Path
from typing import List, Optional, Tuple

import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('Agg')  # headless
import matplotlib.pyplot as plt

try:
    import rasterio
    from rasterio.transform import Affine
except Exception as e:
    raise RuntimeError(f"rasterio is required to run this script: {e}")

# JRC encoding defaults (kept consistent with prediction/restoration.py)
JRC_VALUES = {
    'no_data': 255,
    'land': 1,
    'water': 2,
}
# Try to import authoritative JRC_VALUES if available
try:
    from data.dataset import JRC_VALUES as _JRC_VALUES
    JRC_VALUES = _JRC_VALUES
except Exception:
    pass

EARTH_RADIUS_M = 6378137.0  # WGS84 semi-major; spherical approximation for area formula


def is_geographic_crs(crs) -> bool:
    try:
        return crs is not None and crs.is_geographic
    except Exception:
        # Fallback: string check
        try:
            return str(crs).upper().startswith('EPSG:4326')
        except Exception:
            return False


def parse_timestamp_from_name(name: str) -> Optional[pd.Timestamp]:
    """Parse timestamp from filename.
    Supports patterns:
    - YYYYMMDD_HHMM
    - YYYYMMDD
    - YYYYMM
    Returns pandas.Timestamp or None if not found.
    """
    stem = Path(name).stem
    m = re.search(r"(\d{8})_(\d{4})", stem)
    if m:
        datestr, timestr = m.group(1), m.group(2)
        try:
            return pd.to_datetime(datestr + timestr, format="%Y%m%d%H%M")
        except Exception:
            pass
    m = re.search(r"(\d{8})", stem)
    if m:
        datestr = m.group(1)
        try:
            return pd.to_datetime(datestr, format="%Y%m%d")
        except Exception:
            pass
    m = re.search(r"(\d{6})", stem)
    if m:
        ym = m.group(1)
        try:
            return pd.to_datetime(ym, format="%Y%m")
        except Exception:
            pass
    return None


def per_row_pixel_area_geographic(transform: Affine, height: int, width: int) -> np.ndarray:
    """Compute per-row pixel area (m^2) for a north-up geographic raster.
    Uses spherical formula A = R^2 * dλ * (sin φ2 - sin φ1).
    Returns array of shape (height,) with the area of one pixel for each row.
    """
    # Check rotation terms
    if not np.isclose(transform.b, 0.0) or not np.isclose(transform.d, 0.0):
        raise ValueError("Rotated/skewed transforms are not supported for geographic area computation.")

    dlon_deg = abs(transform.a)
    dlat_deg = abs(transform.e)
    top_lat = transform.f
    # Note: transform.e typically negative; rows increase downward

    dlon_rad = math.radians(dlon_deg)
    dlat_rad = math.radians(dlat_deg)  # not directly used; compute with sin term per row

    # Row edge latitudes: φ(row) at top edge; φ(row+1) at bottom edge
    # top_edge_lat(row) = top_lat + row * transform.e
    rows = np.arange(height, dtype=np.float64)
    lat1 = top_lat + rows * transform.e
    lat2 = top_lat + (rows + 1) * transform.e

    # Convert to radians
    lat1_rad = np.radians(lat1)
    lat2_rad = np.radians(lat2)

    # Clamp to valid range to avoid NaNs if slightly beyond [-90, 90]
    lat1_rad = np.clip(lat1_rad, -math.pi / 2, math.pi / 2)
    lat2_rad = np.clip(lat2_rad, -math.pi / 2, math.pi / 2)

    area_per_pixel_row = (EARTH_RADIUS_M ** 2) * dlon_rad * np.abs(np.sin(lat2_rad) - np.sin(lat1_rad))
    return area_per_pixel_row  # length = height


def per_pixel_area_projected(transform: Affine) -> float:
    """Per-pixel area (m^2) for projected CRS: absolute determinant of affine."""
    # Area of a pixel parallelogram = |det([[a, b], [d, e]])|
    return float(abs(transform.a * transform.e - transform.b * transform.d))


def compute_water_area_m2(tif_path: Path) -> Tuple[Optional[pd.Timestamp], float]:
    """Compute total water area (m^2) for one TIF file.
    Returns (timestamp, area_m2). Timestamp may be None if not parsable.
    """
    with rasterio.open(tif_path) as ds:
        arr = ds.read(1)
        nodata = ds.nodata if ds.nodata is not None else JRC_VALUES['no_data']
        water_mask = (arr == JRC_VALUES['water'])
        if nodata is not None:
            valid_mask = (arr != nodata)
            water_mask &= valid_mask
        height, width = arr.shape

        if is_geographic_crs(ds.crs):
            area_per_row = per_row_pixel_area_geographic(ds.transform, height, width)
            # Vectorized row-wise counts
            counts_per_row = water_mask.sum(axis=1).astype(np.float64)
            area_m2 = float(np.dot(counts_per_row, area_per_row))
        else:
            pix_area = per_pixel_area_projected(ds.transform)
            area_m2 = float(water_mask.sum(dtype=np.int64)) * pix_area

        ts = parse_timestamp_from_name(tif_path.name)
        return ts, area_m2


def scan_tifs(tif_dir: Path, pattern: str) -> List[Path]:
    return sorted([p for p in tif_dir.rglob(pattern) if p.suffix.lower() in ('.tif', '.tiff')])


def main():
    parser = argparse.ArgumentParser(description="Compute monthly water area from restored TIFs and plot monthly changes.")
    parser.add_argument('--tif_dir', type=str, required=True, help='Directory containing restored TIFs (e.g., output_dir from restoration).')
    parser.add_argument('--pattern', type=str, default='mosaic_*.tif', help="Glob pattern to find TIFs (default: 'mosaic_*.tif').")
    parser.add_argument('--out_dir', type=str, default=None, help='Directory to save CSV and plot (default: same as tif_dir).')
    parser.add_argument('--csv_name', type=str, default='monthly_water_area.csv', help='Output CSV filename.')
    parser.add_argument('--fig_name', type=str, default='monthly_water_area.png', help='Output figure filename.')

    args = parser.parse_args()
    tif_dir = Path(args.tif_dir)
    out_dir = Path(args.out_dir) if args.out_dir else tif_dir
    out_dir.mkdir(parents=True, exist_ok=True)

    tifs = scan_tifs(tif_dir, args.pattern)
    if not tifs:
        raise FileNotFoundError(f"No TIFs found under {tif_dir} with pattern {args.pattern}")

    records = []
    for tif in tifs:
        ts, area_m2 = compute_water_area_m2(tif)
        if ts is None:
            # Skip files without parsable date
            continue
        records.append({'timestamp': ts, 'area_km2': area_m2 / 1e6, 'file': str(tif)})

    if not records:
        raise RuntimeError("No records with parsable timestamps were found. Check filename patterns.")

    df = pd.DataFrame.from_records(records)
    df['month'] = df['timestamp'].dt.to_period('M').dt.to_timestamp()
    monthly = df.groupby('month', as_index=False)['area_km2'].sum().sort_values('month')

    csv_path = out_dir / args.csv_name
    monthly.to_csv(csv_path, index=False)

    # Plot
    plt.figure(figsize=(10, 4))
    plt.plot(monthly['month'], monthly['area_km2'], marker='o')
    plt.grid(True, alpha=0.3)
    plt.title('Monthly Water Area (km^2)')
    plt.xlabel('Month')
    plt.ylabel('Water Area (km^2)')
    plt.tight_layout()
    fig_path = out_dir / args.fig_name
    plt.savefig(fig_path, dpi=150)

    # Simple stdout summary
    print(f"Processed {len(df)} files, covering {len(monthly)} months.")
    print(f"CSV saved to: {csv_path}")
    print(f"Figure saved to: {fig_path}")


if __name__ == '__main__':
    main()

