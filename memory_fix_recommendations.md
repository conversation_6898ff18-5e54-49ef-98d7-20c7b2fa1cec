# 内存累积问题修复建议

## 立即需要修复的高优先级问题

### 1. 修复GDAL缓存配置

**当前问题**: 每个进程都设置50%系统内存的GDAL缓存，多进程时总缓存超出系统内存。

**修复方案**:
```python
# 在 data/glad/clip_glad.py 和 data/clip.py 中修改
def configure_gdal_cache(max_processes):
    """根据进程数动态配置GDAL缓存"""
    try:
        total_mem_mb = int(psutil.virtual_memory().total / (1024 ** 2))
        # 为系统保留30%内存，剩余70%在所有进程间分配
        available_mem = int(total_mem_mb * 0.7)
        gdal_cache_per_process = max(256, int(available_mem / max_processes))
        # 单个进程最大不超过8GB
        gdal_cache_per_process = min(gdal_cache_per_process, 8192)
        os.environ["GDAL_CACHEMAX"] = str(gdal_cache_per_process)
        logger.info(f"Set GDAL cache to {gdal_cache_per_process}MB per process")
    except Exception:
        os.environ.setdefault("GDAL_CACHEMAX", "1024")

# 在 process_dataset 函数开始时调用
configure_gdal_cache(max_workers)
```

### 2. 优化大型数组分配策略

**当前问题**: 在 `create_netcdf_file()` 中同时分配多个大型数组。

**修复方案**:
```python
def create_netcdf_file_optimized(output_file, window_attrs, tile_results, time_coords):
    """内存优化版本的NetCDF文件创建"""
    try:
        if not tile_results:
            logger.warning(f"No tile results to write for {output_file}")
            return

        # 确定维度
        max_idx_x = max(result['idx_x'] for result in tile_results) + 1
        max_idx_y = max(result['idx_y'] for result in tile_results) + 1
        num_time_steps = len(time_coords)

        # 估算内存需求
        main_array_size = max_idx_x * max_idx_y * num_time_steps * TILE_SIZE * TILE_SIZE
        estimated_gb = main_array_size / (1024**3)
        
        if estimated_gb > 20:  # 如果超过20GB，使用分块写入
            logger.info(f"Large array detected ({estimated_gb:.1f}GB), using chunked writing")
            return _create_netcdf_chunked(output_file, window_attrs, tile_results, time_coords)
        
        # 小数组直接处理
        return _create_netcdf_direct(output_file, window_attrs, tile_results, time_coords)
        
    except Exception as e:
        logger.error(f"Error creating NetCDF file {output_file}: {str(e)}")
        raise

def _create_netcdf_chunked(output_file, window_attrs, tile_results, time_coords):
    """分块写入大型NetCDF文件"""
    # 使用xarray的分块写入功能，避免同时分配所有数组
    # 实现细节...
    pass

def _create_netcdf_direct(output_file, window_attrs, tile_results, time_coords):
    """直接写入小型NetCDF文件"""
    # 原有的实现逻辑
    pass
```

### 3. 改进线程缓存清理机制

**当前问题**: 线程局部缓存清理不彻底，特别是在多进程环境下。

**修复方案**:
```python
import atexit
import weakref

# 全局跟踪所有线程的缓存
_all_thread_caches = weakref.WeakSet()

class LRUCache:
    def __init__(self, capacity):
        self.cache = OrderedDict()
        self.capacity = capacity
        # 注册到全局跟踪
        _all_thread_caches.add(self)

    def close_all(self):
        """关闭所有缓存的dataset"""
        for key, ds in list(self.cache.items()):
            try:
                if hasattr(ds, 'close') and not ds.closed:
                    ds.close()
            except Exception as e:
                logger.warning(f"Error closing dataset {key}: {e}")
        self.cache.clear()

def cleanup_all_thread_caches():
    """清理所有线程的缓存"""
    for cache in list(_all_thread_caches):
        try:
            cache.close_all()
        except Exception:
            pass

# 在每个工作函数结束时调用
def _process_window_task(args):
    try:
        # 原有逻辑...
        return result
    finally:
        # 确保清理当前线程的缓存
        if hasattr(_thread_local, "ds_cache"):
            _thread_local.ds_cache.close_all()
        gc.collect()

# 注册程序退出时的全局清理
atexit.register(cleanup_all_thread_caches)
```

## 中优先级修复建议

### 4. 减少多进程数据传递开销

**修复方案**:
```python
# 使用共享内存或文件缓存减少数据传递
def process_dataset_optimized(glad_dir, output_dir="output/glad_large_windows", processes=32):
    # 将 lon_lat_groups 保存到临时文件，进程间共享文件路径而非数据
    import tempfile
    import pickle
    
    with tempfile.NamedTemporaryFile(mode='wb', delete=False) as f:
        pickle.dump(lon_lat_groups, f)
        shared_data_path = f.name
    
    try:
        # 传递文件路径而非数据本身
        with ProcessPoolExecutor(max_workers=max_workers) as global_pool:
            for lon_lat, group in tqdm(lon_lat_groups.items(), desc="Processing images"):
                # 只传递必要的数据
                task_args = (group, shared_data_path, ...)  # 减少参数大小
                
    finally:
        # 清理临时文件
        try:
            os.unlink(shared_data_path)
        except Exception:
            pass
```

### 5. 改进memmap文件管理

**修复方案**:
```python
import tempfile
from pathlib import Path

class MemmapManager:
    def __init__(self, base_dir=None):
        self.base_dir = Path(base_dir) if base_dir else Path(tempfile.gettempdir())
        self.active_files = set()
        
    def create_memmap(self, data, prefix="tile"):
        """创建memmap文件并跟踪"""
        path = self.base_dir / f"{prefix}_{os.getpid()}_{id(data)}.mmap"
        mm = np.memmap(path, dtype=data.dtype, mode='w+', shape=data.shape)
        mm[:] = data[:]
        mm.flush()
        self.active_files.add(path)
        return str(path)
    
    def cleanup_all(self):
        """清理所有memmap文件"""
        for path in list(self.active_files):
            try:
                if path.exists():
                    path.unlink()
                self.active_files.discard(path)
            except Exception as e:
                logger.warning(f"Failed to remove memmap file {path}: {e}")

# 全局memmap管理器
_memmap_manager = MemmapManager()
atexit.register(_memmap_manager.cleanup_all)
```

## 监控和诊断建议

### 6. 添加内存监控

```python
import psutil
import threading
import time

class MemoryMonitor:
    def __init__(self, interval=30):
        self.interval = interval
        self.running = False
        self.thread = None
        
    def start(self):
        self.running = True
        self.thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.thread.start()
        
    def stop(self):
        self.running = False
        if self.thread:
            self.thread.join()
            
    def _monitor_loop(self):
        while self.running:
            try:
                process = psutil.Process()
                memory_info = process.memory_info()
                memory_percent = process.memory_percent()
                
                logger.info(f"Memory usage: {memory_info.rss / 1024**3:.2f}GB "
                           f"({memory_percent:.1f}% of system)")
                
                # 检查是否接近内存限制
                if memory_percent > 80:
                    logger.warning(f"High memory usage detected: {memory_percent:.1f}%")
                    gc.collect()  # 强制垃圾回收
                    
            except Exception as e:
                logger.error(f"Memory monitoring error: {e}")
                
            time.sleep(self.interval)

# 在主函数中启动监控
def process_dataset(glad_dir, output_dir, processes=32):
    monitor = MemoryMonitor()
    monitor.start()
    
    try:
        # 原有处理逻辑...
        pass
    finally:
        monitor.stop()
```

## 实施优先级

1. **立即实施** (本周内):
   - 修复GDAL缓存配置
   - 改进线程缓存清理

2. **短期实施** (2周内):
   - 优化大型数组分配策略
   - 添加内存监控

3. **中期实施** (1个月内):
   - 减少多进程数据传递开销
   - 改进memmap文件管理

这些修复将显著减少内存累积问题，提高系统稳定性。
