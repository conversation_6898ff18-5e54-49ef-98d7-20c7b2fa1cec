#!/bin/bash
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=64
#SBATCH --nodes=1
#SBATCH --account=geog_geors
#SBATCH --partition=c_foss_amd
#SBATCH --qos=normal
#SBATCH --time=100:00:00
#SBATCH --output=logs/clip_glad.out
#SBATCH --mem=700G
#SBATCH --mail-type=END
#SBATCH --mail-user=<EMAIL>

# Usage:
# sbatch run_clip.sh /path/to/jrc_dir /path/to/occ_dir /path/to/output_dir [processes]

JRC_DIR=${1:-/fossfs/xiaozhen/JRC_GSW/monthly}
OCC_DIR=${2:-/fossfs/xiaozhen/JRC_GSW/occurrence}
OUT_DIR=${3:-/fossfs/xiaozhen/Clip/JRC4}
PROCESSES=${4:-16}

pwd
# Initialize Conda
source /home/<USER>/miniconda/bin/activate
# Activate the geospatial environment
conda activate geospatial

python data/clip.py --jrc_dir "$JRC_DIR" --jrc_occurrence_dir "$OCC_DIR" --output_dir "$OUT_DIR" --processes "$PROCESSES"