"""
Inpainting-Only Loss Function for SwinWaterNet with Water Probability Learning

Combines regression losses for probability prediction with frequency-based weighting.
Enhanced with numerical stability improvements for direct probability learning (0-100 values).
Now includes probability DICE loss combined with MSE loss.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import logging
from typing import Dict, List, Optional, Tuple
import numpy as np
from evaluation.metrics import MetricsCalculator

logger = logging.getLogger(__name__)


class InpaintingLossWithWaterWeight(nn.Module):
    """
    Enhanced inpainting loss with improved numerical stability
    Uses weighted MSE loss for probability prediction with frequency-based weighting
    Now combines MSE and DICE losses for better probability learning
    """
    
    def __init__(self, 
                 confidence_weight=0.3,
                 use_frequency_weight=True,
                 use_focal_loss=True,
                 focal_gamma=2.0,
                 mse_weight=0.5,
                 dice_weight=0.5,
                 dice_smooth=1e-6):
        super().__init__()
        
        self.confidence_weight = confidence_weight
        self.use_frequency_weight = use_frequency_weight
        self.use_focal_loss = use_focal_loss
        self.focal_gamma = focal_gamma
        self.mse_weight = mse_weight
        self.dice_weight = dice_weight
        self.dice_smooth = dice_smooth
        
        # Loss bounds for numerical stability
        self.register_buffer('max_loss_value', torch.tensor(10.0))
        self.register_buffer('min_loss_value', torch.tensor(1e-6))
        
        # metrics calculation
        self.metrics_calculator = MetricsCalculator()

    def to(self, device):
        """Ensures all buffers and parameters are moved to the specified device"""
        super().to(device)
        return self
    
    def _safe_log(self, x, eps=1e-7):
        """Numerically stable logarithm"""
        return torch.log(torch.clamp(x, min=eps))
    
    def _safe_exp(self, x, max_val=10.0):
        """Numerically stable exponential"""
        return torch.exp(torch.clamp(x, max=-max_val, min=max_val))
    
    def _compute_probability_mse_loss(self, predictions: torch.Tensor, targets: torch.Tensor, 
                                    mask: torch.Tensor, frequency_weight: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Compute vectorized probability MSE loss with dynamic degree weighting
        
        Args:
            predictions: (B, H, W) - probability predictions in [0, 1]
            targets: (B, H, W) - ground truth probabilities in [0, 1] or [0, 100]
            mask: (H, W) - valid mask
            frequency_weight: (B, H, W) - optional frequency weights
            
        Returns:
            mse_loss: scalar tensor
        """
        device = predictions.device
        valid_mask = mask.bool()
        
        if valid_mask.sum() == 0:
            return torch.tensor(0.1, device=device, requires_grad=True)
        
        # Normalize targets to [0, 1] range if they're in [0, 100] range
        targets_normalized = targets.clone()
        if targets_normalized.max() > 1.0:
            targets_normalized = targets_normalized / 100.0
        
        # Vectorized computation: expand mask to match batch dimension
        # mask: (H, W) -> (1, H, W) -> (B, H, W)
        batch_size = predictions.shape[0]
        mask_expanded = valid_mask.unsqueeze(0).expand(batch_size, -1, -1)  # (B, H, W)
        
        # Apply mask to all batch elements at once
        pred_valid = predictions[mask_expanded]  # (N_total_valid,)
        target_valid = targets_normalized[mask_expanded]  # (N_total_valid,)
        
        if pred_valid.numel() == 0:
            return torch.tensor(0.1, device=device, requires_grad=True)
        
        # Apply frequency weights if available
        if frequency_weight is not None:
            weight_valid = frequency_weight[mask_expanded]  # (N_total_valid,)
            # Normalize weights
            weight_sum = torch.clamp(weight_valid.sum(), min=1e-6)
            weight_valid = weight_valid / weight_sum
        else:
            weight_valid = torch.ones_like(pred_valid)
        
        # Compute MSE Loss
        mse_loss_all = F.mse_loss(pred_valid, target_valid, reduction='none')  # (N_total_valid,)
        
        # Apply Focal Loss if enabled
        if self.use_focal_loss:
            # 计算预测误差的focal weight
            error = torch.abs(pred_valid - target_valid)
            focal_weight = (1 - error) ** self.focal_gamma
            mse_loss_all = focal_weight * mse_loss_all
        
        # Apply frequency weights to MSE
        weighted_mse_loss = (mse_loss_all * weight_valid).sum() / torch.clamp(weight_valid.sum(), min=1e-6)
        weighted_mse_loss = torch.clamp(weighted_mse_loss, min=1e-6, max=10.0)
        
        # Safety checks
        if torch.isnan(weighted_mse_loss) or torch.isinf(weighted_mse_loss):
            logger.warning("NaN/Inf in MSE loss, using fallback")
            return torch.tensor(0.5, device=device, requires_grad=True)
        
        return weighted_mse_loss

    def _compute_probability_dice_loss(self, predictions: torch.Tensor, targets: torch.Tensor, 
                                     mask: torch.Tensor, frequency_weight: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Compute vectorized probability DICE loss with dynamic degree weighting
        
        Args:
            predictions: (B, H, W) - probability predictions in [0, 1]
            targets: (B, H, W) - ground truth probabilities in [0, 1] or [0, 100]
            mask: (H, W) - valid mask
            frequency_weight: (B, H, W) - optional frequency weights
            
        Returns:
            dice_loss: scalar tensor
        """
        device = predictions.device
        valid_mask = mask.bool()
        
        if valid_mask.sum() == 0:
            return torch.tensor(0.1, device=device, requires_grad=True)
        
        # Normalize targets to [0, 1] range if they're in [0, 100] range
        targets_normalized = targets.clone()
        if targets_normalized.max() > 1.0:
            targets_normalized = targets_normalized / 100.0
        
        # Vectorized computation: expand mask to match batch dimension
        # mask: (H, W) -> (1, H, W) -> (B, H, W)
        batch_size = predictions.shape[0]
        mask_expanded = valid_mask.unsqueeze(0).expand(batch_size, -1, -1)  # (B, H, W)
        
        # Apply mask to all batch elements at once
        pred_valid = predictions[mask_expanded]  # (N_total_valid,)
        target_valid = targets_normalized[mask_expanded]  # (N_total_valid,)
        
        if pred_valid.numel() == 0:
            return torch.tensor(0.1, device=device, requires_grad=True)
        
        # Apply frequency weights if available
        if frequency_weight is not None:
            weight_valid = frequency_weight[mask_expanded]  # (N_total_valid,)
            # Normalize weights
            weight_sum = torch.clamp(weight_valid.sum(), min=1e-6)
            weight_valid = weight_valid / weight_sum
        else:
            weight_valid = torch.ones_like(pred_valid)
        
        # Compute weighted intersection and union for DICE
        intersection = (pred_valid * target_valid * weight_valid).sum()
        union = (pred_valid * weight_valid).sum() + (target_valid * weight_valid).sum()
        
        # DICE coefficient with smoothing
        dice_coeff = (2.0 * intersection + self.dice_smooth) / (union + self.dice_smooth)
        
        # DICE loss = 1 - DICE coefficient
        dice_loss = 1.0 - dice_coeff
        
        # Safety checks
        dice_loss = torch.clamp(dice_loss, min=1e-6, max=1.0)
        
        if torch.isnan(dice_loss) or torch.isinf(dice_loss):
            logger.warning("NaN/Inf in DICE loss, using fallback")
            return torch.tensor(0.5, device=device, requires_grad=True)
        
        return dice_loss

    def forward(self, outputs: Dict, batch: Dict, stage: int = 1) -> Tuple[torch.Tensor, Dict]:
        """
        Compute inpainting loss with enhanced numerical stability for probability learning
        
        Args:
            outputs: Model outputs containing 'inpaint' predictions
            batch: Batch data containing ground truth, masks, and frequency info
            stage: Training stage (for compatibility)
        """
        # Ensure loss function is on the same device as inputs
        device = batch['ground_truth'].device
        self.to(device)
        
        losses = {}
        metrics = {}
        
        try:
            # 提取数据 - ground_truth现在是概率值 (0-100)
            target = batch['ground_truth']  # (B, H, W) - probability values
            
            # missing_mask 是中心帧二维掩码 (H, W)
            mask = batch['missing_mask']
                        
            # Get water frequency if available
            water_frequency = batch.get('occurrence') if self.use_frequency_weight else None
            
            # Get predictions
            if isinstance(outputs.get('inpaint'), dict):
                pred_logits = outputs['inpaint']['logits']  # (B, 1, H, W) - single channel for probability
                pred_confidence = outputs['inpaint'].get('confidence')
            else:
                pred_logits = outputs.get('inpaint', outputs)
                pred_confidence = outputs.get('confidence')
            
            # Convert logits to probabilities (0-1 range)
            if pred_logits.shape[1] == 2:  # If still 2-channel, take first channel
                pred_probabilities = torch.sigmoid(pred_logits[:, 0, :, :])  # (B, H, W)
            else:  # Single channel
                pred_probabilities = torch.sigmoid(pred_logits.squeeze(1))  # (B, H, W)
            
            # Enhanced input validation
            if torch.isnan(pred_probabilities).any() or torch.isinf(pred_probabilities).any():
                logger.warning("NaN/Inf detected in predictions, applying correction")
                pred_probabilities = torch.nan_to_num(pred_probabilities, nan=0.5, posinf=1.0, neginf=0.0)
                pred_probabilities = torch.clamp(pred_probabilities, min=0.0, max=1.0)
            
            # Compute combined loss (MSE + DICE)
            combined_loss, metrics = self._compute_combined_loss(
                pred_probabilities, target, mask, water_frequency
            )
            
            # Safety check for main loss
            if torch.isnan(combined_loss) or torch.isinf(combined_loss):
                logger.warning("NaN detected in combined loss, using fallback")
                combined_loss = torch.tensor(1.0, device=device, requires_grad=True)
            
            total_loss = combined_loss
            
            # Confidence loss with enhanced stability
            if pred_confidence is not None and not torch.isnan(pred_confidence).any():
                try:
                    conf_loss = self._compute_confidence_loss(pred_confidence, target, mask, pred_probabilities)
                    if not (torch.isnan(conf_loss) or torch.isinf(conf_loss)):
                        losses['confidence'] = conf_loss
                        total_loss += self.confidence_weight * conf_loss
                except Exception as e:
                    logger.warning(f"Error computing confidence loss: {e}")
            
            # Final safety checks
            total_loss = torch.clamp(total_loss, 
                                   min=self.min_loss_value.to(device), 
                                   max=self.max_loss_value.to(device))
            
            if torch.isnan(total_loss) or torch.isinf(total_loss):
                logger.warning("NaN in final loss, using safe fallback")
                total_loss = torch.tensor(1.0, device=device, requires_grad=True)

            return total_loss, metrics
            
        except Exception as e:
            logger.error(f"Error in loss computation: {e}")
            # Return safe fallback loss
            device = batch['ground_truth'].device if 'ground_truth' in batch else torch.device('cuda:0')
            safe_loss = torch.tensor(1.0, device=device, requires_grad=True)
            return safe_loss, {}
    
    def _compute_combined_loss(self, predictions: torch.Tensor, targets: torch.Tensor, 
                              mask: torch.Tensor, water_frequency: Optional[torch.Tensor]) -> Tuple[torch.Tensor, Dict]:
        """
        Compute combined MSE and DICE loss with dynamic degree weighting using vectorized operations
        """
        # Validate inputs
        device = predictions.device
        # 强制将掩码转换为布尔类型，防止后续按位运算报错
        valid_mask = mask.bool()
        if valid_mask.sum() == 0:
            print("valid_mask.sum() == 0")
            return torch.tensor(0.1, device=device, requires_grad=True), {}
        
        # 更强的数值稳定性： 将极端值剪切并检查NaN
        predictions = torch.nan_to_num(predictions, nan=0.5, posinf=1.0, neginf=0.0)
        predictions = torch.clamp(predictions, min=0.0, max=1.0)

        # Calculate frequency weights using dynamic degree function
        if self.use_frequency_weight and water_frequency is not None:
            frequency_weight = compute_dynamic_degree(water_frequency)
        else:
            frequency_weight = torch.ones_like(mask.float())
        
        # Compute MSE Loss with vectorized operations
        weighted_mse_loss = self._compute_probability_mse_loss(predictions, targets, mask, frequency_weight)
        
        # Compute DICE Loss with vectorized operations
        dice_loss = self._compute_probability_dice_loss(predictions, targets, mask, frequency_weight)
        
        # Combine losses with specified weights
        combined_loss = self.mse_weight * weighted_mse_loss + self.dice_weight * dice_loss
        
        # Final NaN/Inf check
        if torch.isnan(combined_loss) or torch.isinf(combined_loss):
            logger.warning("NaN in combined loss, using fallback")
            return torch.tensor(0.5, device=device, requires_grad=True), {}
        
        # Compute metrics safely
        with torch.no_grad():
            # Convert to binary for metrics - use consistent thresholding
            # For GLAD data: predictions are 0-1, targets are 0-100
            # Use 0.5 threshold for both (equivalent to 50 for targets)
            pred_binary = (predictions > 0.5).bool()
            
            # Normalize targets to 0-1 range first, then apply same threshold
            targets_normalized = targets.clone()
            if targets_normalized.max() > 1.0:
                targets_normalized = targets_normalized / 100.0
            target_binary = (targets_normalized > 0.5).bool()
            
            valid_mask_bool = valid_mask.bool()

            # Debug: Print some statistics to understand the data distribution
            # Only print occasionally to avoid spam
            if torch.rand(1).item() < 0.01:  # 1% chance to print
                pred_water_ratio = pred_binary.float().mean().item()
                target_water_ratio = target_binary.float().mean().item()
                valid_ratio = valid_mask_bool.float().mean().item()
                logger.info(f"Debug - Pred water ratio: {pred_water_ratio:.4f}, Target water ratio: {target_water_ratio:.4f}, Valid ratio: {valid_ratio:.4f}")
                logger.info(f"Debug - MSE Loss: {weighted_mse_loss.item():.4f}, DICE Loss: {dice_loss.item():.4f}, Combined: {combined_loss.item():.4f}")

            metrics = {}
            if water_frequency is not None:
                metrics = self.metrics_calculator.calculate_metrics(
                    pred_binary, target_binary, valid_mask_bool, water_frequency
                )
            
        return combined_loss, metrics

    def _compute_weighted_loss(self, predictions: torch.Tensor, targets: torch.Tensor, 
                              mask: torch.Tensor, water_frequency: Optional[torch.Tensor]) -> Tuple[torch.Tensor, Dict]:
        """
        Legacy method - now calls _compute_combined_loss for backward compatibility
        """
        return self._compute_combined_loss(predictions, targets, mask, water_frequency)
    
    def _compute_confidence_loss(self, confidence: torch.Tensor, target: torch.Tensor, 
                                missing_mask: torch.Tensor, predictions: torch.Tensor) -> torch.Tensor:
        """Compute confidence loss with enhanced numerical stability for probability learning"""
        device = confidence.device
        # 保证掩码为布尔类型
        valid_mask = missing_mask.bool()
        if valid_mask.sum() == 0:
            return torch.tensor(0.1, device=device, requires_grad=True)
        
        # Enhanced input validation
        confidence = torch.nan_to_num(confidence, nan=0.0, posinf=5.0, neginf=-5.0)
        confidence = torch.clamp(confidence, min=-5, max=5)
        
        # Target computation with safety checks - for probability learning
        with torch.no_grad():
            # Normalize targets to [0, 1] range if they're in [0, 100] range
            target_normalized = target.clone()
            if target_normalized.max() > 1.0:
                target_normalized = target_normalized / 100.0
            
            # Compute prediction error as confidence target
            # Lower error = higher confidence
            error = torch.abs(predictions - target_normalized)
            conf_target = torch.exp(-error) * valid_mask.float()  # exp(-error) gives higher confidence for lower error
        
        if valid_mask.sum() == 0:
            return torch.tensor(0.1, device=device, requires_grad=True)
        
        # Enhanced BCE computation
        try:
            pos_weight = torch.ones_like(conf_target[valid_mask])
            conf_loss = F.binary_cross_entropy_with_logits(
                confidence.squeeze(1)[valid_mask],
                conf_target[valid_mask],
                pos_weight=pos_weight,
                reduction='mean'
            )
            
            conf_loss = torch.clamp(conf_loss, min=1e-6, max=10.0)
            
            if torch.isnan(conf_loss) or torch.isinf(conf_loss):
                return torch.tensor(0.1, device=device, requires_grad=True)
            
            return conf_loss
            
        except Exception as e:
            logger.warning(f"Error in confidence loss: {e}")
            return torch.tensor(0.1, device=device, requires_grad=True)


# For backward compatibility
InpaintingOnlyLoss = InpaintingLossWithWaterWeight


def compute_smart_water_weight(frequency: torch.Tensor,
                              min_weight: float = 0.1,
                              max_weight: float = 1.0,
                              sensitivity: float = 2.0) -> torch.Tensor:
    """
    智能水体权重计算，使用统计归一化方法
    
    核心思想：将频率值相对于图像统计特性进行归一化，确保权重差异明显
    
    Args:
        frequency: (B, H, W) 或 (B, H_p, W_p) - 水体频率图
        min_weight: 最小权重
        max_weight: 最大权重
        sensitivity: 敏感度因子，控制权重差异的强度
        
    Returns:
        weight: 权重图，形状与输入相同
    """
    epsilon = 1e-6
    freq_clamped = torch.clamp(frequency, epsilon, 1.0 - epsilon)
    
    # 计算每个batch的统计特性
    if freq_clamped.dim() == 4:  # (B, H, W)
        mean_freq = freq_clamped.mean(dim=(1, 2), keepdim=True)  # (B, 1, 1)
        std_freq = freq_clamped.std(dim=(1, 2), keepdim=True) + epsilon  # (B, 1, 1)
    else:  # (B, H_p, W_p)
        mean_freq = freq_clamped.mean(dim=(1, 2), keepdim=True)  # (B, 1, 1)
        std_freq = freq_clamped.std(dim=(1, 2), keepdim=True) + epsilon  # (B, 1, 1)
    
    # Z-score归一化：将频率转换为标准正态分布
    z_score = (freq_clamped - mean_freq) / std_freq
    
    # 使用tanh函数将Z-score映射到[-1, 1]，然后映射到权重范围
    # tanh函数在0附近线性，在极值处饱和，非常适合这种应用
    normalized_weight = torch.tanh(z_score * sensitivity)
    
    # 将[-1, 1]映射到[min_weight, max_weight]
    weight = min_weight + (max_weight - min_weight) * (normalized_weight + 1) / 2
    
    return weight


def compute_dynamic_degree(frequency: torch.Tensor, 
                          alpha: float = 2.0, 
                          sigma: float = 0.25,
                          min_weight: float = 0.01,
                          max_weight: float = 2.0) -> torch.Tensor:
    """
    计算动态程度，使用非线性高斯权重公式
    
    Args:
        frequency: (B, H, W) 或 (B, H_p, W_p) - 水体频率图
        alpha: 最大权重倍数，默认1.0
        sigma: 控制曲线宽度，默认0.25
        min_weight: 最小权重，默认0.01
        max_weight: 最大权重，默认1.0
        
    Returns:
        dynamic_degree: (B, H, W) 或 (B, H_p, W_p) - 动态程度权重
    """
    # 数值稳定性处理
    epsilon = 1e-6
    freq_clamped = torch.clamp(frequency, epsilon, 1.0 - epsilon)
    
    # 使用"鞍形"权重曲线，对中间频率(0.4-0.6)给予更高权重
    # w = α * exp(-((f-0.5)/σ)^2)
    dynamic_degree = alpha * torch.exp(-((freq_clamped - 0.5) / sigma) ** 2)
    dynamic_degree = torch.clamp(dynamic_degree, min_weight, max_weight)
    
    return dynamic_degree


 